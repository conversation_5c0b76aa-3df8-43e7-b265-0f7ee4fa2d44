# Web界面使用指南

## 🌐 Web界面概述

Obsidian插件同步器提供了一个现代化的Web管理界面，让你可以通过浏览器轻松管理插件同步。

## 🚀 快速启动

### 方法1：一键启动（推荐）
```bash
python start_web.py
```
这将自动：
- 检查并安装依赖
- 启动Web服务器
- 在浏览器中打开界面

### 方法2：手动启动
```bash
python main.py web
```

### 自定义配置
```bash
# 自定义主机和端口
python main.py web --host 0.0.0.0 --port 9000

# 允许外部访问
python main.py web --host 0.0.0.0 --port 8080
```

## 📱 界面功能

### 主仪表板

#### 状态卡片
- **总插件数**: 显示社区插件总数
- **已下载**: 显示已成功下载的插件数量
- **调度器状态**: 显示自动同步调度器的运行状态
- **上次同步**: 显示最后一次同步的时间

#### 操作按钮
- **刷新**: 更新所有状态信息
- **开始同步**: 手动触发完整同步
- **更新插件**: 只更新有新版本的插件
- **调度器控制**: 启动/停止自动同步调度器

### 插件列表

#### 搜索和过滤
- **搜索框**: 按插件名称、作者或描述搜索
- **状态过滤**: 按插件状态筛选
  - 所有状态
  - 已下载
  - 已更新
  - 错误
  - 可用

#### 插件卡片信息
每个插件卡片显示：
- 插件名称和ID
- 作者信息
- 描述
- GitHub仓库链接
- 当前状态（带颜色标识）
- 版本信息
- 详情按钮

#### 状态颜色说明
- 🟢 **绿色边框**: 已下载的插件
- 🔵 **蓝色边框**: 已更新的插件
- 🔴 **红色边框**: 下载失败的插件
- ⚪ **默认边框**: 其他状态

### 插件详情

点击"详情"按钮可查看：
- **基本信息**: ID、名称、作者、状态
- **版本信息**: 当前版本、最新版本、更新时间
- **描述**: 插件功能描述
- **仓库链接**: GitHub仓库地址
- **本地路径**: 插件文件存储位置
- **错误信息**: 如果有下载错误，显示详细信息

## 🔧 API接口

Web界面基于RESTful API构建，你也可以直接调用API：

### 状态接口
```bash
GET /api/status
```
返回系统状态和统计信息

### 插件列表
```bash
GET /api/plugins?page=1&limit=20&status=downloaded&search=dataview
```
参数：
- `page`: 页码（默认1）
- `limit`: 每页数量（默认50）
- `status`: 状态过滤
- `search`: 搜索关键词

### 插件详情
```bash
GET /api/plugins/{plugin_id}
```

### 同步操作
```bash
POST /api/sync
POST /api/update
```

### 调度器控制
```bash
POST /api/scheduler/start
POST /api/scheduler/stop
```

### 同步历史
```bash
GET /api/sync-history?limit=10
```

## 🛠️ 自定义配置

### 修改端口
如果8080端口被占用：
```bash
python main.py web --port 9000
```

### 允许外部访问
如果需要从其他设备访问：
```bash
python main.py web --host 0.0.0.0 --port 8080
```
然后通过 `http://你的IP:8080` 访问

### 反向代理
可以使用Nginx等反向代理：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔒 安全注意事项

1. **本地访问**: 默认只允许本地访问（127.0.0.1）
2. **无认证**: 当前版本没有用户认证，请勿暴露到公网
3. **防火墙**: 如果开放外部访问，注意防火墙设置

## 🐛 故障排除

### 端口被占用
```bash
# 检查端口占用
netstat -an | grep 8080

# 使用其他端口
python main.py web --port 9000
```

### 浏览器无法访问
1. 检查防火墙设置
2. 确认服务器正常启动
3. 尝试使用 `127.0.0.1` 而不是 `localhost`

### 静态文件加载失败
确保 `static/` 和 `templates/` 目录存在：
```bash
mkdir -p static templates
```

### 依赖问题
重新安装Web相关依赖：
```bash
pip install fastapi uvicorn jinja2
```

## 📱 移动端适配

Web界面使用Bootstrap 5构建，完全支持移动设备：
- 响应式设计
- 触摸友好的界面
- 移动端优化的导航

## 🔄 实时更新

界面会定期刷新状态信息，你也可以：
- 点击"刷新"按钮手动更新
- 使用浏览器的刷新功能
- 查看实时的同步进度

## 💡 使用技巧

1. **书签收藏**: 将Web界面添加到浏览器书签
2. **多标签页**: 可以同时打开多个标签页
3. **搜索快捷键**: 在搜索框中按Enter快速搜索
4. **状态监控**: 定期检查调度器状态确保自动同步正常
5. **错误排查**: 通过插件详情查看具体错误信息
