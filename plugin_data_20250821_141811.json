[{"id": "table-editor-obsidian", "name": "Advanced Tables", "author": "<PERSON>", "description": "Improved table navigation, formatting, and manipulation.", "repo": "tgrosinger/advanced-tables-obsidian", "github_url": "https://github.com/tgrosinger/advanced-tables-obsidian", "current_version": "0.22.1", "latest_version": "0.22.1", "status": "downloaded", "download_count": 0, "last_updated": "2025-08-21T14:16:03.277342", "last_checked": "2025-08-21T14:16:03.277342", "local_path": "plugins\\table-editor-obsidian", "error_message": null}, {"id": "dataview", "name": "Dataview", "author": "<PERSON>", "description": "Advanced queries over your vault for the data-obsessed.", "repo": "blacksmithgu/obsidian-dataview", "github_url": "https://github.com/blacksmithgu/obsidian-dataview", "current_version": "0.5.68", "latest_version": "0.5.68", "status": "downloaded", "download_count": 0, "last_updated": "2025-08-21T14:16:12.383402", "last_checked": "2025-08-21T14:16:12.383402", "local_path": "plugins\\dataview", "error_message": null}, {"id": "nldates-obsidian", "name": "Natural Language Dates", "author": "Argentina Ortega Sainz", "description": "Create date-links based on natural language.", "repo": "argenos/nldates-obsidian", "github_url": "https://github.com/argenos/nldates-obsidian", "current_version": "0.6.2", "latest_version": "0.6.2", "status": "downloaded", "download_count": 0, "last_updated": "2025-08-21T14:15:52.334838", "last_checked": "2025-08-21T14:15:52.334838", "local_path": "plugins\\nldates-obsidian", "error_message": null}, {"id": "templater-obsidian", "name": "<PERSON><PERSON><PERSON><PERSON>", "author": "SilentVoid", "description": "Create and use templates.", "repo": "SilentVoid13/Templater", "github_url": "https://github.com/SilentVoid13/Templater", "current_version": "2.14.1", "latest_version": "2.14.1", "status": "downloaded", "download_count": 0, "last_updated": "2025-08-21T14:16:07.816153", "last_checked": "2025-08-21T14:16:07.816153", "local_path": "plugins\\templater-obsidian", "error_message": null}, {"id": "test-plugin", "name": "Test Plugin", "author": "Test Author", "description": "Test Description", "repo": "test/test-plugin", "github_url": "https://github.com/test/test-plugin", "current_version": null, "latest_version": null, "status": "unknown", "download_count": 0, "last_updated": null, "last_checked": null, "local_path": null, "error_message": null}]