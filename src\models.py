"""数据模型定义"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class PluginStatus(str, Enum):
    """插件状态枚举"""
    UNKNOWN = "unknown"
    AVAILABLE = "available"
    DOWNLOADED = "downloaded"
    UPDATED = "updated"
    ERROR = "error"


class PluginInfo(BaseModel):
    """插件基础信息（来自community-plugins.json）"""
    id: str
    name: str
    author: str
    description: str
    repo: str


class PluginManifest(BaseModel):
    """插件清单信息（来自manifest.json）"""
    id: str
    name: str
    version: str
    minAppVersion: Optional[str] = None
    description: str
    author: str
    authorUrl: Optional[str] = None
    fundingUrl: Optional[Any] = None  # 可能是字符串或字典
    isDesktopOnly: bool = False


class GitHubRelease(BaseModel):
    """GitHub发布信息"""
    tag_name: str
    name: str
    published_at: datetime
    assets: List[Dict[str, Any]] = Field(default_factory=list)
    download_url: Optional[str] = None


class PluginRecord(BaseModel):
    """插件记录（数据库存储）"""
    id: str
    name: str
    author: str
    description: str
    repo: str
    current_version: Optional[str] = None
    latest_version: Optional[str] = None
    status: PluginStatus = PluginStatus.UNKNOWN
    download_count: int = 0
    last_updated: Optional[datetime] = None
    last_checked: Optional[datetime] = None
    local_path: Optional[str] = None
    manifest_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    
    class Config:
        use_enum_values = True


class SyncStats(BaseModel):
    """同步统计信息"""
    total_plugins: int = 0
    downloaded_plugins: int = 0
    updated_plugins: int = 0
    failed_plugins: int = 0
    sync_start_time: Optional[datetime] = None
    sync_end_time: Optional[datetime] = None
    duration_seconds: float = 0.0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_plugins == 0:
            return 0.0
        return (self.downloaded_plugins + self.updated_plugins) / self.total_plugins * 100


class APIRateLimit(BaseModel):
    """API速率限制信息"""
    limit: int
    remaining: int
    reset_time: datetime
    used: int = 0
