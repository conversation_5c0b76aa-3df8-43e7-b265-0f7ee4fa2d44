#!/usr/bin/env python3
"""插件数据分析专用脚本"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.analytics import analytics
from src.database import PluginDatabase
from src.logger import logger


def print_header(title: str):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print('='*60)


def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'-'*40}")
    print(f"  {title}")
    print('-'*40)


def main():
    """主函数"""
    print("🔍 Obsidian插件数据分析工具")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查数据库
    db = PluginDatabase()
    plugins = db.get_all_plugins()
    
    if not plugins:
        print("❌ 没有找到插件数据，请先运行同步命令:")
        print("   python main.py sync --all")
        return
    
    print(f"📊 找到 {len(plugins)} 个插件记录")
    
    # 1. 基础统计
    print_header("基础统计信息")
    
    stats = analytics.get_plugin_rankings(50)
    author_analysis = analytics.get_author_analysis()
    
    print(f"总插件数: {len(plugins)}")
    print(f"总作者数: {author_analysis.get('total_authors', 0)}")
    print(f"独立作者数: {author_analysis.get('single_plugin_authors', 0)}")
    
    # 按状态统计
    status_count = {}
    for plugin in plugins:
        try:
            status = plugin.status.value if hasattr(plugin.status, 'value') else str(plugin.status)
            status_count[status] = status_count.get(status, 0) + 1
        except Exception as e:
            print(f"处理插件 {plugin.name} 状态时出错: {e}")
            print(f"状态类型: {type(plugin.status)}, 值: {plugin.status}")
            status_count['error'] = status_count.get('error', 0) + 1
    
    print("\n插件状态分布:")
    for status, count in status_count.items():
        print(f"  {status}: {count} 个")
    
    # 2. 最新插件
    print_section("最新上架插件 (Top 10)")
    newest = stats.get('newest', [])[:10]
    for i, plugin in enumerate(newest, 1):
        print(f"{i:2d}. {plugin['name']} - {plugin['author']}")
        print(f"     仓库: https://github.com/{plugin['repo']}")
        if plugin.get('created_at'):
            print(f"     上架: {plugin['created_at'][:10]}")
        print()
    
    # 3. 最近更新
    print_section("最近更新插件 (Top 10)")
    updated = stats.get('recently_updated', [])[:10]
    for i, plugin in enumerate(updated, 1):
        print(f"{i:2d}. {plugin['name']} - {plugin['author']}")
        print(f"     版本: {plugin.get('current_version', '未知')}")
        if plugin.get('last_updated'):
            print(f"     更新: {plugin['last_updated'][:10]}")
        print()
    
    # 4. 高产作者
    print_section("高产作者 (Top 10)")
    authors = stats.get('top_authors', [])[:10]
    for i, author in enumerate(authors, 1):
        print(f"{i:2d}. {author['author']}")
        print(f"     插件数: {author['plugin_count']} 个")
        print(f"     下载量: {author.get('total_downloads', 0):,}")
        if author.get('latest_update'):
            print(f"     活动: {author['latest_update'][:10]}")
        print()
    
    # 5. 插件类别
    print_section("插件类别分析")
    categories = analytics.get_plugin_categories()
    
    for category, data in categories.get('categories', {}).items():
        print(f"{category}: {data['count']} 个插件")
        # 显示该类别的热门插件
        top_plugins = [p['name'] for p in data['plugins'][:3]]
        if top_plugins:
            print(f"  热门: {', '.join(top_plugins)}")
        print()
    
    uncategorized = categories.get('uncategorized_count', 0)
    if uncategorized > 0:
        print(f"未分类插件: {uncategorized} 个")
    
    # 6. 作者分布
    print_section("作者分布分析")
    for dist in author_analysis.get('author_distribution', []):
        print(f"{dist['category']}: {dist['count']} 人")
    
    # 7. 时间线分析
    print_section("最近30天活动")
    timeline = analytics.get_plugin_timeline(30)
    if timeline:
        summary = timeline.get('summary', {})
        print(f"新增插件: {summary.get('total_new', 0)} 个")
        print(f"更新插件: {summary.get('total_updated', 0)} 个")
        print(f"日均新增: {summary.get('avg_new_per_day', 0):.1f} 个")
        print(f"日均更新: {summary.get('avg_updated_per_day', 0):.1f} 个")
    
    # 8. 导出选项
    print_header("数据导出选项")
    print("可用的导出命令:")
    print("1. 导出JSON格式: python main.py export --format json")
    print("2. 导出CSV格式:  python main.py export --format csv")
    print("3. 导出Excel格式: python main.py export --format excel")
    print("4. 生成分析报告: python main.py report")
    print("5. 查看排行榜:   python main.py rankings")
    print("6. 查看类别分析: python main.py categories")
    
    print("\n✅ 分析完成！")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  分析被用户中断")
    except Exception as e:
        print(f"\n\n💥 分析过程中发生错误: {e}")
        logger.error(f"分析失败: {e}")
        sys.exit(1)
