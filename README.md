# Obsidian插件同步器

一个强大的工具，用于自动同步和管理Obsidian社区插件市场的所有插件。

## 功能特性

### 🚀 核心功能
- **完整插件同步**: 获取Obsidian社区插件市场中所有可用插件
- **智能版本管理**: 自动检测和下载插件更新
- **本地插件库**: 维护完整的本地插件副本
- **增量同步**: 只下载新插件和更新，节省时间和带宽

### 🛡️ 技术优势
- **API速率限制处理**: 智能避免GitHub API限制
- **并发下载**: 支持多线程并发下载，提高效率
- **错误恢复**: 自动重试机制，处理网络异常
- **数据完整性**: 文件验证确保下载完整性

### 📊 管理功能
- **定时同步**: 可配置的自动同步间隔
- **详细统计**: 同步历史和成功率统计
- **过滤系统**: 支持包含/排除特定插件
- **进度跟踪**: 实时显示同步进度
- **Web界面**: 现代化的Web管理界面
- **实时监控**: 可视化插件状态和同步进度
- **数据分析**: 插件排行榜、作者统计、类别分析
- **数据导出**: 支持JSON、CSV、Excel格式导出

## 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 基本使用

1. **首次同步所有插件**:
```bash
python main.py sync --all
```

2. **更新已有插件**:
```bash
python main.py update
```

3. **查看状态**:
```bash
python main.py status
```

4. **启动Web界面**:
```bash
python start_web.py
# 或者
python main.py web
```

5. **启动守护进程**:
```bash
python main.py daemon
```

## 详细使用指南

### 命令行界面

#### 同步命令
```bash
# 同步新插件
python main.py sync

# 强制重新下载所有插件
python main.py sync --all

# 只更新过期插件
python main.py update
```

#### 状态查看
```bash
# 查看整体状态
python main.py status

# 查看特定插件信息
python main.py info <plugin-id>
```

#### Web界面管理
```bash
# 启动Web界面（推荐）
python start_web.py

# 自定义主机和端口
python main.py web --host 0.0.0.0 --port 8080
```

Web界面功能：
- 📊 实时状态监控
- 🔍 插件搜索和过滤
- ⚡ 一键同步和更新
- 📈 同步历史统计
- ⚙️ 调度器控制

#### 数据分析
```bash
# 快速分析插件数据
python analyze_plugins.py

# 查看插件排行榜
python main.py rankings

# 查看插件类别分析
python main.py categories

# 生成详细分析报告
python main.py report

# 导出插件数据
python main.py export --format json
python main.py export --format csv
python main.py export --format excel

# 数据库查询示例
python query_examples.py
```

#### 配置管理
```bash
# 设置GitHub Token（提高API限制）
python main.py config --token YOUR_GITHUB_TOKEN

# 设置自动同步间隔
python main.py config --interval 12

# 启用/禁用自动同步
python main.py config --enable-auto
python main.py config --disable-auto
```

### 配置文件

编辑 `config.yaml` 文件来自定义设置：

```yaml
# GitHub API配置
github:
  token: ""  # 可选，用于提高API速率限制
  request_interval: 1.0  # 请求间隔（秒）
  max_retries: 3
  timeout: 30

# 存储配置
storage:
  plugins_dir: "./plugins"  # 插件下载目录
  database_path: "./obsidian_plugins.db"
  log_file: "./logs/sync.log"

# 同步配置
sync:
  auto_sync_interval: 24  # 自动同步间隔（小时）
  enable_auto_sync: true
  max_concurrent_downloads: 5
  download_all: false  # 是否下载所有插件

# 过滤配置
filters:
  excluded_plugins: []  # 排除的插件ID
  included_plugins: []  # 只包含的插件ID（为空则包含所有）
  min_downloads: 0  # 最小下载量过滤
```

## 技术架构

### 核心模块

1. **插件获取器** (`plugin_fetcher.py`)
   - 从obsidian-releases仓库获取插件列表
   - 解析插件manifest信息
   - 获取GitHub发布信息

2. **GitHub API客户端** (`github_api.py`)
   - 处理GitHub API交互
   - 下载插件文件
   - 速率限制管理

3. **数据库管理** (`database.py`)
   - SQLite数据库存储
   - 插件记录管理
   - 同步历史跟踪

4. **插件管理器** (`plugin_manager.py`)
   - 核心同步逻辑
   - 版本比较和更新
   - 文件完整性验证

5. **调度器** (`scheduler.py`)
   - 定时任务管理
   - 后台同步服务
   - 任务队列处理

### 数据流程

```
社区插件列表 → 插件信息获取 → 版本检查 → 文件下载 → 验证存储 → 数据库更新
```

## API速率限制解决方案

### 1. GitHub API优化
- **Personal Access Token**: 提高速率限制从60/小时到5000/小时
- **请求间隔控制**: 可配置的请求间隔避免触发限制
- **智能重试**: 指数退避重试机制

### 2. 缓存策略
- **本地数据库**: 避免重复请求相同信息
- **增量更新**: 只检查变更的插件
- **批量处理**: 合并多个API请求

### 3. 备用方案
- **Raw文件访问**: 直接访问GitHub raw文件，绕过API限制
- **镜像源**: 支持配置镜像源地址
- **离线模式**: 基于已有数据工作

## 文件结构

```
obsidian-plugins-updating/
├── src/                    # 源代码目录
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── logger.py          # 日志系统
│   ├── models.py          # 数据模型
│   ├── plugin_fetcher.py  # 插件信息获取
│   ├── github_api.py      # GitHub API集成
│   ├── database.py        # 数据库管理
│   ├── plugin_manager.py  # 插件管理核心
│   ├── scheduler.py       # 同步调度器
│   └── utils.py           # 工具函数
├── plugins/               # 插件下载目录
├── logs/                  # 日志文件目录
├── main.py               # 主程序入口
├── config.yaml           # 配置文件
├── requirements.txt      # Python依赖
└── README.md            # 说明文档
```

## 常见问题

### Q: GitHub API速率限制怎么办？
A: 配置GitHub Personal Access Token可以将限制提高到5000次/小时。同时程序内置了智能重试和间隔控制。

### Q: 如何只同步特定插件？
A: 在config.yaml中配置`filters.included_plugins`列表，只包含需要的插件ID。

### Q: 插件下载失败怎么办？
A: 程序会自动重试，并在日志中记录详细错误信息。可以查看logs目录下的日志文件。

### Q: 如何设置代理？
A: 可以通过环境变量设置HTTP代理：
```bash
export HTTP_PROXY=http://proxy.example.com:8080
export HTTPS_PROXY=http://proxy.example.com:8080
```

## 贡献指南

欢迎提交Issue和Pull Request！

## 许可证

MIT License
