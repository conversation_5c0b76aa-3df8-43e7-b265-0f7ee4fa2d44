"""插件管理核心模块"""
import asyncio
import json
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from .models import PluginInfo, PluginRecord, PluginStatus, SyncStats
from .plugin_fetcher import PluginFetcher
from .github_api import GitHubAPI
from .database import PluginDatabase
from .config import config
from .logger import logger


class PluginManager:
    """插件管理器"""
    
    def __init__(self):
        self.db = PluginDatabase()
        self.plugins_dir = Path(config.storage.plugins_dir)
        self.plugins_dir.mkdir(parents=True, exist_ok=True)
    
    async def sync_all_plugins(self) -> SyncStats:
        """同步所有插件"""
        stats = SyncStats(sync_start_time=datetime.now())
        
        try:
            logger.info("开始同步所有插件...")
            
            # 获取社区插件列表
            async with PluginFetcher() as fetcher:
                community_plugins = await fetcher.get_community_plugins()

                stats.total_plugins = len(community_plugins)
                logger.info(f"发现 {stats.total_plugins} 个社区插件")

                # 应用过滤器
                filtered_plugins = self._apply_filters(community_plugins)
                logger.info(f"过滤后剩余 {len(filtered_plugins)} 个插件")

                # 处理插件
                async with GitHubAPI() as github:
                    for plugin in filtered_plugins:
                        try:
                            success = await self._process_plugin(plugin, github, fetcher)
                            if success:
                                stats.downloaded_plugins += 1
                            else:
                                stats.failed_plugins += 1

                        except Exception as e:
                            logger.error(f"处理插件 {plugin.name} 失败: {e}")
                            stats.failed_plugins += 1
            
            stats.sync_end_time = datetime.now()
            stats.duration_seconds = (stats.sync_end_time - stats.sync_start_time).total_seconds()
            
            # 保存统计信息
            self.db.save_sync_stats(stats)
            
            logger.info(f"同步完成: 成功 {stats.downloaded_plugins}, 失败 {stats.failed_plugins}, "
                       f"耗时 {stats.duration_seconds:.1f}秒, 成功率 {stats.success_rate:.1f}%")
            
            return stats
            
        except Exception as e:
            logger.error(f"同步过程中发生错误: {e}")
            stats.sync_end_time = datetime.now()
            stats.duration_seconds = (stats.sync_end_time - stats.sync_start_time).total_seconds()
            return stats
    
    async def update_outdated_plugins(self) -> SyncStats:
        """更新过期插件"""
        stats = SyncStats(sync_start_time=datetime.now())
        
        try:
            logger.info("检查插件更新...")
            
            # 获取需要更新的插件
            outdated_plugins = self.db.get_outdated_plugins()
            stats.total_plugins = len(outdated_plugins)
            
            if not outdated_plugins:
                logger.info("所有插件都是最新版本")
                return stats
            
            logger.info(f"发现 {len(outdated_plugins)} 个插件需要更新")
            
            async with GitHubAPI() as github:
                async with PluginFetcher() as fetcher:
                    for plugin_record in outdated_plugins:
                        try:
                            # 转换为PluginInfo
                            plugin_info = PluginInfo(
                                id=plugin_record.id,
                                name=plugin_record.name,
                                author=plugin_record.author,
                                description=plugin_record.description,
                                repo=plugin_record.repo
                            )
                            
                            success = await self._update_plugin(plugin_info, github, fetcher)
                            if success:
                                stats.updated_plugins += 1
                            else:
                                stats.failed_plugins += 1
                                
                        except Exception as e:
                            logger.error(f"更新插件 {plugin_record.name} 失败: {e}")
                            stats.failed_plugins += 1
            
            stats.sync_end_time = datetime.now()
            stats.duration_seconds = (stats.sync_end_time - stats.sync_start_time).total_seconds()
            
            self.db.save_sync_stats(stats)
            
            logger.info(f"更新完成: 成功 {stats.updated_plugins}, 失败 {stats.failed_plugins}")
            return stats
            
        except Exception as e:
            logger.error(f"更新过程中发生错误: {e}")
            stats.sync_end_time = datetime.now()
            return stats
    
    async def _process_plugin(self, plugin: PluginInfo, github: GitHubAPI, fetcher: PluginFetcher) -> bool:
        """处理单个插件"""
        try:
            # 检查是否已存在
            existing_record = self.db.get_plugin(plugin.id)
            
            # 获取最新版本信息
            manifest = await fetcher.get_plugin_manifest(plugin.repo)
            if not manifest:
                logger.warning(f"无法获取插件 {plugin.name} 的manifest")
                self.db.update_plugin_status(plugin.id, PluginStatus.ERROR, "无法获取manifest")
                return False
            
            # 检查是否需要下载
            need_download = False
            if not existing_record:
                need_download = True
                logger.info(f"新插件: {plugin.name}")
            elif existing_record.current_version != manifest.version:
                need_download = True
                logger.info(f"插件更新: {plugin.name} {existing_record.current_version} -> {manifest.version}")
            elif config.sync.download_all:
                need_download = True
            
            if need_download:
                # 下载插件文件
                plugin_dir = self.plugins_dir / plugin.id
                success = await github.download_plugin_files(plugin, manifest.version, self.plugins_dir)
                
                if success:
                    # 验证文件
                    if await github.validate_plugin_files(plugin_dir):
                        # 更新数据库记录
                        record = PluginRecord(
                            id=plugin.id,
                            name=plugin.name,
                            author=plugin.author,
                            description=plugin.description,
                            repo=plugin.repo,
                            current_version=manifest.version,
                            latest_version=manifest.version,
                            status=PluginStatus.DOWNLOADED,
                            last_updated=datetime.now(),
                            last_checked=datetime.now(),
                            local_path=str(plugin_dir),
                            manifest_data=manifest.model_dump()
                        )
                        
                        self.db.save_plugin(record)
                        return True
                    else:
                        self.db.update_plugin_status(plugin.id, PluginStatus.ERROR, "文件验证失败")
                        return False
                else:
                    self.db.update_plugin_status(plugin.id, PluginStatus.ERROR, "下载失败")
                    return False
            else:
                # 只更新检查时间
                if existing_record:
                    existing_record.last_checked = datetime.now()
                    existing_record.latest_version = manifest.version
                    self.db.save_plugin(existing_record)
                
                return True
                
        except Exception as e:
            logger.error(f"处理插件 {plugin.name} 时发生错误: {e}")
            self.db.update_plugin_status(plugin.id, PluginStatus.ERROR, str(e))
            return False
    
    async def _update_plugin(self, plugin: PluginInfo, github: GitHubAPI, fetcher: PluginFetcher) -> bool:
        """更新单个插件"""
        try:
            manifest = await fetcher.get_plugin_manifest(plugin.repo)
            if not manifest:
                return False
            
            plugin_dir = self.plugins_dir / plugin.id
            success = await github.download_plugin_files(plugin, manifest.version, self.plugins_dir)
            
            if success and await github.validate_plugin_files(plugin_dir):
                # 更新数据库记录
                existing_record = self.db.get_plugin(plugin.id)
                if existing_record:
                    existing_record.current_version = manifest.version
                    existing_record.latest_version = manifest.version
                    existing_record.status = PluginStatus.UPDATED
                    existing_record.last_updated = datetime.now()
                    existing_record.manifest_data = manifest.model_dump()
                    
                    self.db.save_plugin(existing_record)
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"更新插件 {plugin.name} 失败: {e}")
            return False
    
    def _apply_filters(self, plugins: List[PluginInfo]) -> List[PluginInfo]:
        """应用过滤器"""
        filtered = plugins
        
        # 包含列表过滤
        if config.filters.included_plugins:
            filtered = [p for p in filtered if p.id in config.filters.included_plugins]
        
        # 排除列表过滤
        if config.filters.excluded_plugins:
            filtered = [p for p in filtered if p.id not in config.filters.excluded_plugins]
        
        return filtered
    
    def get_plugin_statistics(self) -> Dict[str, Any]:
        """获取插件统计信息"""
        return self.db.get_statistics()
    
    def get_sync_history(self, limit: int = 10) -> List[SyncStats]:
        """获取同步历史"""
        return self.db.get_sync_history(limit)
