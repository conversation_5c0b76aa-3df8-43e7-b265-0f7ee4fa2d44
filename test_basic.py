#!/usr/bin/env python3
"""基础功能测试脚本"""
import asyncio
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.plugin_fetcher import fetch_all_plugins
from src.config import config
from src.logger import logger


async def test_fetch_plugins():
    """测试获取插件列表"""
    print("🔍 测试获取社区插件列表...")
    
    try:
        plugins = await fetch_all_plugins()
        print(f"✅ 成功获取 {len(plugins)} 个插件")
        
        # 显示前5个插件
        print("\n📋 前5个插件:")
        for i, plugin in enumerate(plugins[:5]):
            print(f"  {i+1}. {plugin.name} ({plugin.id}) - {plugin.author}")
        
        return True
    except Exception as e:
        print(f"❌ 获取插件列表失败: {e}")
        return False


async def test_github_api():
    """测试GitHub API"""
    print("\n🔗 测试GitHub API连接...")
    
    try:
        from src.github_api import GitHubAPI
        
        async with GitHubAPI() as github:
            # 测试获取一个知名插件的统计信息
            stats = await github.get_plugin_download_stats("blacksmithgu/obsidian-dataview")
            print(f"✅ GitHub API连接正常")
            print(f"   Dataview插件下载量: {stats.get('total_downloads', 0)}")
        
        return True
    except Exception as e:
        print(f"❌ GitHub API测试失败: {e}")
        return False


def test_database():
    """测试数据库"""
    print("\n💾 测试数据库连接...")
    
    try:
        from src.database import PluginDatabase
        from src.models import PluginRecord, PluginStatus
        
        db = PluginDatabase()
        
        # 测试插入一条记录
        test_plugin = PluginRecord(
            id="test-plugin",
            name="Test Plugin",
            author="Test Author",
            description="Test Description",
            repo="test/test-plugin",
            status=PluginStatus.UNKNOWN
        )
        
        success = db.save_plugin(test_plugin)
        if success:
            print("✅ 数据库连接正常")
            
            # 测试查询
            retrieved = db.get_plugin("test-plugin")
            if retrieved and retrieved.name == "Test Plugin":
                print("✅ 数据库读写正常")
                return True
            else:
                print("❌ 数据库读取失败")
                return False
        else:
            print("❌ 数据库写入失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


def test_config():
    """测试配置系统"""
    print("\n⚙️  测试配置系统...")
    
    try:
        print(f"✅ 配置加载成功")
        print(f"   插件目录: {config.storage.plugins_dir}")
        print(f"   数据库路径: {config.storage.database_path}")
        print(f"   自动同步: {config.sync.enable_auto_sync}")
        print(f"   同步间隔: {config.sync.auto_sync_interval} 小时")
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 Obsidian插件同步器 - 基础功能测试\n")
    
    tests = [
        ("配置系统", test_config),
        ("数据库", test_database),
        ("插件列表获取", test_fetch_plugins),
        ("GitHub API", test_github_api),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print(f"\n{'='*50}")
    print("测试结果汇总")
    print('='*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统准备就绪。")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接。")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 测试过程中发生未处理的异常: {e}")
        sys.exit(1)
