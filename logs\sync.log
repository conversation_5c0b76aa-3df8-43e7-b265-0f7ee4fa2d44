2025-08-21 14:09:43 - obsidian_sync - ERROR - 保存插件记录失败 test-plugin: 'str' object has no attribute 'value'
2025-08-21 14:09:43 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:09:44 - obsidian_sync - ERROR - 获取社区插件列表失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/plain; charset=utf-8', url='https://raw.githubusercontent.com/obsidianmd/obsidian-releases/master/community-plugins.json'
2025-08-21 14:10:21 - obsidian_sync - ERROR - 保存插件记录失败 test-plugin: 'str' object has no attribute 'value'
2025-08-21 14:10:21 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:10:21 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:10:53 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:10:54 - obsidian_sync - ERROR - 获取社区插件列表失败: Cannot connect to host raw.githubusercontent.com:443 ssl:default [None]
2025-08-21 14:11:59 - obsidian_sync - INFO - 启动Web服务器: http://127.0.0.1:8080
2025-08-21 14:12:27 - obsidian_sync - ERROR - 分析失败: 'str' object has no attribute 'value'
2025-08-21 14:12:52 - obsidian_sync - ERROR - 分析失败: 'str' object has no attribute 'value'
2025-08-21 14:13:35 - obsidian_sync - INFO - 开始同步所有插件...
2025-08-21 14:13:35 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:13:35 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:13:35 - obsidian_sync - INFO - 发现 2602 个社区插件
2025-08-21 14:13:35 - obsidian_sync - INFO - 过滤后剩余 5 个插件
2025-08-21 14:13:36 - obsidian_sync - ERROR - 获取插件 argenos/nldates-obsidian 的manifest.json失败: Session is closed
2025-08-21 14:13:36 - obsidian_sync - WARNING - 无法获取插件 Natural Language Dates 的manifest
2025-08-21 14:13:37 - obsidian_sync - ERROR - 获取插件 Vinzent03/obsidian-git 的manifest.json失败: Session is closed
2025-08-21 14:13:37 - obsidian_sync - WARNING - 无法获取插件 Git 的manifest
2025-08-21 14:13:38 - obsidian_sync - ERROR - 获取插件 tgrosinger/advanced-tables-obsidian 的manifest.json失败: Session is closed
2025-08-21 14:13:38 - obsidian_sync - WARNING - 无法获取插件 Advanced Tables 的manifest
2025-08-21 14:13:39 - obsidian_sync - ERROR - 获取插件 SilentVoid13/Templater 的manifest.json失败: Session is closed
2025-08-21 14:13:39 - obsidian_sync - WARNING - 无法获取插件 Templater 的manifest
2025-08-21 14:13:40 - obsidian_sync - ERROR - 获取插件 blacksmithgu/obsidian-dataview 的manifest.json失败: Session is closed
2025-08-21 14:13:40 - obsidian_sync - WARNING - 无法获取插件 Dataview 的manifest
2025-08-21 14:13:40 - obsidian_sync - INFO - 同步完成: 成功 0, 失败 5, 耗时 5.6秒, 成功率 0.0%
2025-08-21 14:14:21 - obsidian_sync - INFO - 开始同步所有插件...
2025-08-21 14:14:21 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:14:22 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:14:22 - obsidian_sync - INFO - 发现 2602 个社区插件
2025-08-21 14:14:22 - obsidian_sync - INFO - 过滤后剩余 5 个插件
2025-08-21 14:14:23 - obsidian_sync - ERROR - 获取插件 argenos/nldates-obsidian 的manifest.json失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/plain; charset=utf-8', url='https://raw.githubusercontent.com/argenos/nldates-obsidian/master/manifest.json'
2025-08-21 14:14:23 - obsidian_sync - WARNING - 无法获取插件 Natural Language Dates 的manifest
2025-08-21 14:14:24 - obsidian_sync - ERROR - 获取插件 Vinzent03/obsidian-git 的manifest.json失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/plain; charset=utf-8', url='https://raw.githubusercontent.com/Vinzent03/obsidian-git/master/manifest.json'
2025-08-21 14:14:24 - obsidian_sync - WARNING - 无法获取插件 Git 的manifest
2025-08-21 14:14:25 - obsidian_sync - ERROR - 获取插件 tgrosinger/advanced-tables-obsidian 的manifest.json失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/plain; charset=utf-8', url='https://raw.githubusercontent.com/tgrosinger/advanced-tables-obsidian/master/manifest.json'
2025-08-21 14:14:25 - obsidian_sync - WARNING - 无法获取插件 Advanced Tables 的manifest
2025-08-21 14:14:27 - obsidian_sync - ERROR - 获取插件 SilentVoid13/Templater 的manifest.json失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/plain; charset=utf-8', url='https://raw.githubusercontent.com/SilentVoid13/Templater/master/manifest.json'
2025-08-21 14:14:27 - obsidian_sync - WARNING - 无法获取插件 Templater 的manifest
2025-08-21 14:14:28 - obsidian_sync - ERROR - 获取插件 blacksmithgu/obsidian-dataview 的manifest.json失败: 200, message='Attempt to decode JSON with unexpected mimetype: text/plain; charset=utf-8', url='https://raw.githubusercontent.com/blacksmithgu/obsidian-dataview/master/manifest.json'
2025-08-21 14:14:28 - obsidian_sync - WARNING - 无法获取插件 Dataview 的manifest
2025-08-21 14:14:28 - obsidian_sync - INFO - 同步完成: 成功 0, 失败 5, 耗时 6.8秒, 成功率 0.0%
2025-08-21 14:14:51 - obsidian_sync - INFO - 开始同步所有插件...
2025-08-21 14:14:51 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:14:51 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:14:51 - obsidian_sync - INFO - 发现 2602 个社区插件
2025-08-21 14:14:51 - obsidian_sync - INFO - 过滤后剩余 5 个插件
2025-08-21 14:14:52 - obsidian_sync - INFO - 新插件: Natural Language Dates
2025-08-21 14:14:52 - obsidian_sync - INFO - 开始下载插件: Natural Language Dates v0.6.2
2025-08-21 14:14:58 - obsidian_sync - INFO - 插件 Natural Language Dates 下载完成
2025-08-21 14:14:58 - obsidian_sync - ERROR - 验证manifest.json失败: name 'json' is not defined
2025-08-21 14:14:59 - obsidian_sync - ERROR - 获取插件 Vinzent03/obsidian-git 的manifest.json失败: 1 validation error for PluginManifest
minAppVersion
  Field required [type=missing, input_value={'author': 'Vinzent', 'au...t', 'version': '2.35.0'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.8/v/missing
2025-08-21 14:14:59 - obsidian_sync - WARNING - 无法获取插件 Git 的manifest
2025-08-21 14:15:00 - obsidian_sync - ERROR - 获取插件 tgrosinger/advanced-tables-obsidian 的manifest.json失败: 1 validation error for PluginManifest
fundingUrl
  Input should be a valid string [type=string_type, input_value={'Github Sponsor': 'https...//paypal.me/tgrosinger'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.8/v/string_type
2025-08-21 14:15:00 - obsidian_sync - WARNING - 无法获取插件 Advanced Tables 的manifest
2025-08-21 14:15:01 - obsidian_sync - INFO - 新插件: Templater
2025-08-21 14:15:01 - obsidian_sync - INFO - 开始下载插件: Templater v2.14.1
2025-08-21 14:15:05 - obsidian_sync - INFO - 插件 Templater 下载完成
2025-08-21 14:15:05 - obsidian_sync - ERROR - 验证manifest.json失败: name 'json' is not defined
2025-08-21 14:15:06 - obsidian_sync - INFO - 新插件: Dataview
2025-08-21 14:15:06 - obsidian_sync - INFO - 开始下载插件: Dataview v0.5.68
2025-08-21 14:15:10 - obsidian_sync - INFO - 插件 Dataview 下载完成
2025-08-21 14:15:10 - obsidian_sync - ERROR - 验证manifest.json失败: name 'json' is not defined
2025-08-21 14:15:10 - obsidian_sync - INFO - 同步完成: 成功 0, 失败 5, 耗时 19.5秒, 成功率 0.0%
2025-08-21 14:15:46 - obsidian_sync - INFO - 开始同步所有插件...
2025-08-21 14:15:46 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:15:47 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:15:47 - obsidian_sync - INFO - 发现 2602 个社区插件
2025-08-21 14:15:47 - obsidian_sync - INFO - 过滤后剩余 5 个插件
2025-08-21 14:15:48 - obsidian_sync - INFO - 新插件: Natural Language Dates
2025-08-21 14:15:48 - obsidian_sync - INFO - 开始下载插件: Natural Language Dates v0.6.2
2025-08-21 14:15:52 - obsidian_sync - INFO - 插件 Natural Language Dates 下载完成
2025-08-21 14:15:53 - obsidian_sync - INFO - 新插件: Git
2025-08-21 14:15:53 - obsidian_sync - INFO - 开始下载插件: Git v2.35.0
2025-08-21 14:15:57 - obsidian_sync - INFO - 插件 Git 下载完成
2025-08-21 14:15:57 - obsidian_sync - ERROR - manifest.json缺少必需字段: minAppVersion
2025-08-21 14:15:58 - obsidian_sync - INFO - 新插件: Advanced Tables
2025-08-21 14:15:58 - obsidian_sync - INFO - 开始下载插件: Advanced Tables v0.22.1
2025-08-21 14:16:03 - obsidian_sync - INFO - 插件 Advanced Tables 下载完成
2025-08-21 14:16:04 - obsidian_sync - INFO - 新插件: Templater
2025-08-21 14:16:04 - obsidian_sync - INFO - 开始下载插件: Templater v2.14.1
2025-08-21 14:16:07 - obsidian_sync - INFO - 插件 Templater 下载完成
2025-08-21 14:16:08 - obsidian_sync - INFO - 新插件: Dataview
2025-08-21 14:16:08 - obsidian_sync - INFO - 开始下载插件: Dataview v0.5.68
2025-08-21 14:16:12 - obsidian_sync - INFO - 插件 Dataview 下载完成
2025-08-21 14:16:12 - obsidian_sync - INFO - 同步完成: 成功 4, 失败 1, 耗时 25.4秒, 成功率 0.2%
2025-08-21 14:16:34 - obsidian_sync - ERROR - 分析失败: 'str' object has no attribute 'value'
2025-08-21 14:17:23 - obsidian_sync - ERROR - 分析失败: 'str' object has no attribute 'value'
2025-08-21 14:18:11 - obsidian_sync - INFO - 插件数据已导出到: plugin_data_20250821_141811.json
2025-08-21 14:18:26 - obsidian_sync - INFO - 分析报告已生成: plugin_analysis_report_20250821_141826.md
2025-08-21 14:19:05 - obsidian_sync - INFO - 开始同步所有插件...
2025-08-21 14:19:05 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:19:05 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:19:05 - obsidian_sync - INFO - 发现 2602 个社区插件
2025-08-21 14:19:05 - obsidian_sync - INFO - 过滤后剩余 9 个插件
2025-08-21 14:19:07 - obsidian_sync - INFO - 新插件: Git
2025-08-21 14:19:07 - obsidian_sync - INFO - 开始下载插件: Git v2.35.0
2025-08-21 14:19:12 - obsidian_sync - INFO - 插件 Git 下载完成
2025-08-21 14:19:12 - obsidian_sync - ERROR - manifest.json缺少必需字段: minAppVersion
2025-08-21 14:19:14 - obsidian_sync - INFO - 新插件: Calendar
2025-08-21 14:19:14 - obsidian_sync - INFO - 开始下载插件: Calendar v1.5.10
2025-08-21 14:19:18 - obsidian_sync - INFO - 插件 Calendar 下载完成
2025-08-21 14:19:21 - obsidian_sync - ERROR - 获取插件 lynchjames/obsidian-mind-map 的manifest.json失败: 1 validation error for PluginManifest
author
  Field required [type=missing, input_value={'id': 'obsidian-mind-map... False, 'js': 'main.js'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.8/v/missing
2025-08-21 14:19:21 - obsidian_sync - WARNING - 无法获取插件 Mind Map 的manifest
2025-08-21 14:19:23 - obsidian_sync - INFO - 新插件: Excalidraw
2025-08-21 14:19:23 - obsidian_sync - INFO - 开始下载插件: Excalidraw v2.15.0
2025-08-21 14:19:27 - obsidian_sync - INFO - 插件 Excalidraw 下载完成
2025-08-21 14:19:29 - obsidian_sync - INFO - 新插件: QuickAdd
2025-08-21 14:19:29 - obsidian_sync - INFO - 开始下载插件: QuickAdd v2.1.0
2025-08-21 14:19:33 - obsidian_sync - INFO - 插件 QuickAdd 下载完成
2025-08-21 14:19:33 - obsidian_sync - INFO - 同步完成: 成功 7, 失败 2, 耗时 28.5秒, 成功率 0.3%
2025-08-21 14:24:15 - obsidian_sync - INFO - 启动Web服务器: http://127.0.0.1:8080
2025-08-21 14:24:17 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:24:17 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:24:37 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:24:37 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:27:23 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:27:23 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:27:59 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:28:00 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:28:29 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:28:30 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:28:46 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:28:46 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:29:17 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:29:17 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:29:47 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:29:47 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:30:17 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:30:17 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:30:47 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:30:47 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:31:13 - obsidian_sync - INFO - 启动Web服务器: http://127.0.0.1:8080
2025-08-21 14:31:15 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:31:16 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:31:16 - obsidian_sync - INFO - 更新官方插件总数缓存: 2602
2025-08-21 14:36:43 - obsidian_sync - INFO - 开始同步所有插件...
2025-08-21 14:36:43 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 14:36:43 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 14:36:43 - obsidian_sync - INFO - 发现 2602 个社区插件
2025-08-21 14:36:43 - obsidian_sync - INFO - 过滤后剩余 9 个插件
2025-08-21 14:36:46 - obsidian_sync - INFO - 新插件: Git
2025-08-21 14:36:46 - obsidian_sync - INFO - 开始下载插件: Git v2.35.0
2025-08-21 14:36:50 - obsidian_sync - INFO - 插件 Git 下载完成
2025-08-21 14:36:50 - obsidian_sync - ERROR - manifest.json缺少必需字段: minAppVersion
2025-08-21 14:36:55 - obsidian_sync - ERROR - 获取插件 lynchjames/obsidian-mind-map 的manifest.json失败: 1 validation error for PluginManifest
author
  Field required [type=missing, input_value={'id': 'obsidian-mind-map... False, 'js': 'main.js'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.8/v/missing
2025-08-21 14:36:55 - obsidian_sync - WARNING - 无法获取插件 Mind Map 的manifest
2025-08-21 14:36:59 - obsidian_sync - INFO - 同步完成: 成功 7, 失败 2, 耗时 16.3秒, 成功率 0.3%
2025-08-21 15:10:45 - obsidian_sync - INFO - 已设置自动同步，间隔: 24 小时
2025-08-21 15:10:45 - obsidian_sync - INFO - 同步调度器已启动
2025-08-21 15:11:06 - obsidian_sync - INFO - 开始同步所有插件...
2025-08-21 15:11:06 - obsidian_sync - INFO - 正在获取社区插件列表...
2025-08-21 15:11:07 - obsidian_sync - INFO - 成功获取 2602 个插件信息
2025-08-21 15:11:07 - obsidian_sync - INFO - 发现 2602 个社区插件
2025-08-21 15:11:07 - obsidian_sync - INFO - 过滤后剩余 9 个插件
2025-08-21 15:11:09 - obsidian_sync - INFO - 新插件: Git
2025-08-21 15:11:09 - obsidian_sync - INFO - 开始下载插件: Git v2.35.0
2025-08-21 15:11:14 - obsidian_sync - INFO - 插件 Git 下载完成
2025-08-21 15:11:14 - obsidian_sync - ERROR - manifest.json缺少必需字段: minAppVersion
2025-08-21 15:11:19 - obsidian_sync - ERROR - 获取插件 lynchjames/obsidian-mind-map 的manifest.json失败: 1 validation error for PluginManifest
author
  Field required [type=missing, input_value={'id': 'obsidian-mind-map... False, 'js': 'main.js'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.8/v/missing
2025-08-21 15:11:19 - obsidian_sync - WARNING - 无法获取插件 Mind Map 的manifest
2025-08-21 15:11:23 - obsidian_sync - INFO - 同步完成: 成功 7, 失败 2, 耗时 16.3秒, 成功率 0.3%
