"""数据分析和统计模块"""
import sqlite3
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import pandas as pd
from collections import defaultdict

from .database import PluginDatabase
from .models import PluginRecord, PluginStatus
from .config import config
from .logger import logger


class PluginAnalytics:
    """插件数据分析器"""
    
    def __init__(self):
        self.db = PluginDatabase()
        self.db_path = Path(config.storage.database_path)
    
    def get_plugin_rankings(self, limit: int = 50) -> Dict[str, List[Dict[str, Any]]]:
        """获取插件排行榜"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                rankings = {}
                
                # 最新上架插件
                cursor = conn.execute("""
                    SELECT id, name, author, repo, created_at, current_version, download_count
                    FROM plugins 
                    WHERE created_at IS NOT NULL
                    ORDER BY created_at DESC 
                    LIMIT ?
                """, (limit,))
                
                rankings['newest'] = [dict(row) for row in cursor.fetchall()]
                
                # 最近更新插件
                cursor = conn.execute("""
                    SELECT id, name, author, repo, last_updated, current_version, latest_version, download_count
                    FROM plugins 
                    WHERE last_updated IS NOT NULL
                    ORDER BY last_updated DESC 
                    LIMIT ?
                """, (limit,))
                
                rankings['recently_updated'] = [dict(row) for row in cursor.fetchall()]
                
                # 下载量排行（如果有数据）
                cursor = conn.execute("""
                    SELECT id, name, author, repo, download_count, current_version, last_updated
                    FROM plugins 
                    WHERE download_count > 0
                    ORDER BY download_count DESC 
                    LIMIT ?
                """, (limit,))
                
                rankings['most_downloaded'] = [dict(row) for row in cursor.fetchall()]
                
                # 活跃作者排行
                cursor = conn.execute("""
                    SELECT author, COUNT(*) as plugin_count, 
                           SUM(download_count) as total_downloads,
                           MAX(last_updated) as latest_update
                    FROM plugins 
                    GROUP BY author 
                    ORDER BY plugin_count DESC 
                    LIMIT ?
                """, (limit,))
                
                rankings['top_authors'] = [dict(row) for row in cursor.fetchall()]
                
                return rankings
                
        except Exception as e:
            logger.error(f"获取插件排行榜失败: {e}")
            return {}
    
    def get_plugin_timeline(self, days: int = 30) -> Dict[str, Any]:
        """获取插件时间线统计"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                # 计算日期范围
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)
                
                timeline = {
                    'period': f"{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}",
                    'daily_stats': [],
                    'summary': {}
                }
                
                # 按日统计新增和更新
                cursor = conn.execute("""
                    SELECT DATE(created_at) as date, COUNT(*) as new_plugins
                    FROM plugins 
                    WHERE created_at >= ? AND created_at <= ?
                    GROUP BY DATE(created_at)
                    ORDER BY date
                """, (start_date.isoformat(), end_date.isoformat()))
                
                new_plugins_by_date = {row['date']: row['new_plugins'] for row in cursor.fetchall()}
                
                cursor = conn.execute("""
                    SELECT DATE(last_updated) as date, COUNT(*) as updated_plugins
                    FROM plugins 
                    WHERE last_updated >= ? AND last_updated <= ?
                    GROUP BY DATE(last_updated)
                    ORDER BY date
                """, (start_date.isoformat(), end_date.isoformat()))
                
                updated_plugins_by_date = {row['date']: row['updated_plugins'] for row in cursor.fetchall()}
                
                # 生成每日统计
                current_date = start_date
                while current_date <= end_date:
                    date_str = current_date.strftime('%Y-%m-%d')
                    timeline['daily_stats'].append({
                        'date': date_str,
                        'new_plugins': new_plugins_by_date.get(date_str, 0),
                        'updated_plugins': updated_plugins_by_date.get(date_str, 0)
                    })
                    current_date += timedelta(days=1)
                
                # 汇总统计
                timeline['summary'] = {
                    'total_new': sum(new_plugins_by_date.values()),
                    'total_updated': sum(updated_plugins_by_date.values()),
                    'avg_new_per_day': sum(new_plugins_by_date.values()) / days,
                    'avg_updated_per_day': sum(updated_plugins_by_date.values()) / days
                }
                
                return timeline
                
        except Exception as e:
            logger.error(f"获取插件时间线失败: {e}")
            return {}
    
    def get_author_analysis(self) -> Dict[str, Any]:
        """获取作者分析"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                analysis = {
                    'total_authors': 0,
                    'author_distribution': [],
                    'prolific_authors': [],
                    'single_plugin_authors': 0
                }
                
                # 作者插件数量分布
                cursor = conn.execute("""
                    SELECT author, COUNT(*) as plugin_count,
                           SUM(download_count) as total_downloads,
                           MAX(last_updated) as latest_activity
                    FROM plugins 
                    GROUP BY author 
                    ORDER BY plugin_count DESC
                """)
                
                authors_data = cursor.fetchall()
                analysis['total_authors'] = len(authors_data)
                
                # 统计分布
                distribution = defaultdict(int)
                for author in authors_data:
                    count = author['plugin_count']
                    if count == 1:
                        distribution['1个插件'] += 1
                        analysis['single_plugin_authors'] += 1
                    elif count <= 3:
                        distribution['2-3个插件'] += 1
                    elif count <= 5:
                        distribution['4-5个插件'] += 1
                    elif count <= 10:
                        distribution['6-10个插件'] += 1
                    else:
                        distribution['10个以上插件'] += 1
                
                analysis['author_distribution'] = [
                    {'category': k, 'count': v} for k, v in distribution.items()
                ]
                
                # 高产作者（3个以上插件）
                analysis['prolific_authors'] = [
                    {
                        'author': author['author'],
                        'plugin_count': author['plugin_count'],
                        'total_downloads': author['total_downloads'] or 0,
                        'latest_activity': author['latest_activity']
                    }
                    for author in authors_data if author['plugin_count'] >= 3
                ]
                
                return analysis
                
        except Exception as e:
            logger.error(f"获取作者分析失败: {e}")
            return {}
    
    def get_plugin_categories(self) -> Dict[str, Any]:
        """分析插件类别（基于描述关键词）"""
        try:
            plugins = self.db.get_all_plugins()
            
            # 定义类别关键词
            categories = {
                '笔记管理': ['note', 'notes', 'notebook', 'journal', 'diary'],
                '编辑器增强': ['editor', 'editing', 'format', 'markdown', 'text'],
                '任务管理': ['task', 'todo', 'gtd', 'kanban', 'project'],
                '数据可视化': ['chart', 'graph', 'diagram', 'visualization', 'plot'],
                '模板系统': ['template', 'snippet', 'boilerplate'],
                '链接管理': ['link', 'backlink', 'reference', 'citation'],
                '同步备份': ['sync', 'backup', 'export', 'import', 'git'],
                '主题美化': ['theme', 'style', 'css', 'appearance', 'ui'],
                '搜索增强': ['search', 'find', 'query', 'filter'],
                '时间管理': ['time', 'calendar', 'schedule', 'reminder'],
                '学习工具': ['study', 'learning', 'flashcard', 'spaced', 'anki'],
                '开发工具': ['code', 'programming', 'developer', 'api', 'plugin']
            }
            
            category_stats = defaultdict(list)
            uncategorized = []
            
            for plugin in plugins:
                description = plugin.description.lower()
                name = plugin.name.lower()
                
                categorized = False
                for category, keywords in categories.items():
                    if any(keyword in description or keyword in name for keyword in keywords):
                        category_stats[category].append({
                            'id': plugin.id,
                            'name': plugin.name,
                            'author': plugin.author,
                            'download_count': plugin.download_count
                        })
                        categorized = True
                        break
                
                if not categorized:
                    uncategorized.append({
                        'id': plugin.id,
                        'name': plugin.name,
                        'description': plugin.description[:100] + '...' if len(plugin.description) > 100 else plugin.description
                    })
            
            # 统计结果
            result = {
                'categories': {},
                'uncategorized_count': len(uncategorized),
                'uncategorized_samples': uncategorized[:20]  # 只显示前20个未分类的
            }
            
            for category, plugins_list in category_stats.items():
                result['categories'][category] = {
                    'count': len(plugins_list),
                    'plugins': sorted(plugins_list, key=lambda x: x['download_count'], reverse=True)[:10]  # 每类显示前10个
                }
            
            return result
            
        except Exception as e:
            logger.error(f"获取插件类别分析失败: {e}")
            return {}
    
    def export_plugin_data(self, format: str = 'json', output_path: Optional[str] = None) -> str:
        """导出插件数据"""
        try:
            plugins = self.db.get_all_plugins()
            
            # 准备导出数据
            export_data = []
            for plugin in plugins:
                data = {
                    'id': plugin.id,
                    'name': plugin.name,
                    'author': plugin.author,
                    'description': plugin.description,
                    'repo': plugin.repo,
                    'github_url': f"https://github.com/{plugin.repo}",
                    'current_version': plugin.current_version,
                    'latest_version': plugin.latest_version,
                    'status': plugin.status.value if hasattr(plugin.status, 'value') else str(plugin.status),
                    'download_count': plugin.download_count,
                    'last_updated': plugin.last_updated.isoformat() if plugin.last_updated else None,
                    'last_checked': plugin.last_checked.isoformat() if plugin.last_checked else None,
                    'local_path': plugin.local_path,
                    'error_message': plugin.error_message
                }
                export_data.append(data)
            
            # 确定输出路径
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"plugin_data_{timestamp}.{format}"
            
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 导出数据
            if format.lower() == 'json':
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            elif format.lower() == 'csv':
                df = pd.DataFrame(export_data)
                df.to_csv(output_file, index=False, encoding='utf-8')
            
            elif format.lower() == 'excel':
                df = pd.DataFrame(export_data)
                df.to_excel(output_file, index=False)
            
            else:
                raise ValueError(f"不支持的格式: {format}")
            
            logger.info(f"插件数据已导出到: {output_file}")
            return str(output_file)
            
        except Exception as e:
            logger.error(f"导出插件数据失败: {e}")
            raise
    
    def generate_report(self, output_path: Optional[str] = None) -> str:
        """生成完整的分析报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if not output_path:
                output_path = f"plugin_analysis_report_{timestamp}.md"
            
            # 收集所有分析数据
            rankings = self.get_plugin_rankings()
            timeline = self.get_plugin_timeline()
            author_analysis = self.get_author_analysis()
            categories = self.get_plugin_categories()
            
            # 生成Markdown报告
            report = f"""# Obsidian插件分析报告

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📊 总体统计

- 总插件数: {len(self.db.get_all_plugins())}
- 总作者数: {author_analysis.get('total_authors', 0)}
- 独立作者数: {author_analysis.get('single_plugin_authors', 0)}

## 🏆 插件排行榜

### 最新上架插件 (Top 10)
"""
            
            # 最新插件
            for i, plugin in enumerate(rankings.get('newest', [])[:10], 1):
                report += f"{i}. **{plugin['name']}** - {plugin['author']}\n"
                report += f"   - 仓库: [{plugin['repo']}](https://github.com/{plugin['repo']})\n"
                report += f"   - 上架时间: {plugin.get('created_at', '未知')}\n\n"
            
            # 最近更新
            report += "\n### 最近更新插件 (Top 10)\n\n"
            for i, plugin in enumerate(rankings.get('recently_updated', [])[:10], 1):
                report += f"{i}. **{plugin['name']}** - {plugin['author']}\n"
                report += f"   - 仓库: [{plugin['repo']}](https://github.com/{plugin['repo']})\n"
                report += f"   - 更新时间: {plugin.get('last_updated', '未知')}\n"
                report += f"   - 版本: {plugin.get('current_version', '未知')}\n\n"
            
            # 下载量排行
            if rankings.get('most_downloaded'):
                report += "\n### 下载量排行 (Top 10)\n\n"
                for i, plugin in enumerate(rankings.get('most_downloaded', [])[:10], 1):
                    report += f"{i}. **{plugin['name']}** - {plugin['author']}\n"
                    report += f"   - 仓库: [{plugin['repo']}](https://github.com/{plugin['repo']})\n"
                    report += f"   - 下载量: {plugin.get('download_count', 0):,}\n\n"
            
            # 作者排行
            report += "\n### 高产作者 (Top 10)\n\n"
            for i, author in enumerate(rankings.get('top_authors', [])[:10], 1):
                report += f"{i}. **{author['author']}**\n"
                report += f"   - 插件数量: {author['plugin_count']}\n"
                report += f"   - 总下载量: {author.get('total_downloads', 0):,}\n"
                report += f"   - 最近活动: {author.get('latest_update', '未知')}\n\n"
            
            # 插件类别
            report += "\n## 📂 插件类别分析\n\n"
            for category, data in categories.get('categories', {}).items():
                report += f"### {category} ({data['count']} 个插件)\n\n"
                for plugin in data['plugins'][:5]:  # 每类显示前5个
                    report += f"- **{plugin['name']}** - {plugin['author']}\n"
                report += "\n"
            
            # 时间线分析
            if timeline:
                report += f"\n## 📈 时间线分析 ({timeline['period']})\n\n"
                report += f"- 新增插件: {timeline['summary']['total_new']} 个\n"
                report += f"- 更新插件: {timeline['summary']['total_updated']} 个\n"
                report += f"- 日均新增: {timeline['summary']['avg_new_per_day']:.1f} 个\n"
                report += f"- 日均更新: {timeline['summary']['avg_updated_per_day']:.1f} 个\n\n"
            
            # 作者分析
            report += "\n## 👥 作者分析\n\n"
            for dist in author_analysis.get('author_distribution', []):
                report += f"- {dist['category']}: {dist['count']} 人\n"
            
            report += f"\n---\n\n*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"
            
            # 保存报告
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"分析报告已生成: {output_file}")
            return str(output_file)
            
        except Exception as e:
            logger.error(f"生成分析报告失败: {e}")
            raise


# 全局分析器实例
analytics = PluginAnalytics()
