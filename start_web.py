#!/usr/bin/env python3
"""Web界面快速启动脚本"""
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def main():
    """主函数"""
    print("🚀 启动Obsidian插件同步器Web界面...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 检查依赖
    try:
        import fastapi
        import uvicorn
    except ImportError:
        print("📦 正在安装Web界面依赖...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    
    # 创建必要的目录
    Path("templates").mkdir(exist_ok=True)
    Path("static").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    # 启动Web服务器
    host = "127.0.0.1"
    port = 8080
    url = f"http://{host}:{port}"
    
    print(f"🌐 启动Web服务器: {url}")
    print("📱 Web界面功能:")
    print("   • 实时查看插件同步状态")
    print("   • 浏览和搜索插件列表")
    print("   • 手动触发同步和更新")
    print("   • 管理调度器设置")
    print("   • 查看同步历史和统计")
    print()
    print("⏳ 正在启动服务器...")
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(2)
        print(f"🔗 自动打开浏览器: {url}")
        webbrowser.open(url)
    
    import threading
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    # 运行Web服务器
    try:
        from main import cli
        sys.argv = ['main.py', 'web', '--host', host, '--port', str(port)]
        cli()
    except KeyboardInterrupt:
        print("\n👋 Web服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n🔧 故障排除:")
        print("1. 确保端口8080未被占用")
        print("2. 检查防火墙设置")
        print("3. 尝试运行: python main.py web --help")

if __name__ == "__main__":
    main()
