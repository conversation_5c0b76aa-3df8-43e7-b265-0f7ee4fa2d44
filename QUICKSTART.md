# 快速开始指南

## 5分钟快速上手

### 1. 环境准备

确保你的系统已安装Python 3.8或更高版本：

```bash
python --version
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 基础测试

运行基础功能测试，确保系统正常：

```bash
python test_basic.py
```

如果所有测试通过，说明系统准备就绪！

### 4. 启动Web界面（推荐）

使用现代化的Web界面管理插件：

```bash
# 一键启动Web界面
python start_web.py
```

Web界面将自动在浏览器中打开：`http://127.0.0.1:8080`

### 5. 或使用命令行

如果你更喜欢命令行：

```bash
# 同步所有插件（首次运行推荐）
python main.py sync --all

# 查看同步状态
python main.py status

# 查看特定插件信息
python main.py info dataview
```

## 进阶配置

### 配置GitHub Token（推荐）

1. 访问 [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/tokens)
2. 创建新token，选择 `public_repo` 权限
3. 配置token：

```bash
python main.py config --token YOUR_GITHUB_TOKEN
```

这将把API限制从60次/小时提高到5000次/小时。

### 启用自动同步

```bash
# 启用自动同步（每24小时）
python main.py config --enable-auto --interval 24

# 启动守护进程
python main.py daemon
```

### 自定义过滤

编辑 `config.yaml` 文件：

```yaml
filters:
  # 只同步这些插件
  included_plugins: ["dataview", "templater-obsidian", "obsidian-git"]
  
  # 或者排除某些插件
  excluded_plugins: ["unwanted-plugin-id"]
```

## 使用方式

### Web界面（推荐）

```bash
# 启动Web界面
python start_web.py

# 自定义端口
python main.py web --port 9000
```

Web界面功能：
- 📊 实时状态监控
- 🔍 插件搜索和过滤
- ⚡ 一键同步和更新
- 📈 同步历史统计
- ⚙️ 调度器控制

### 命令行界面

```bash
# 查看帮助
python main.py --help

# 同步所有插件
python main.py sync --all

# 更新过期插件
python main.py update

# 查看状态
python main.py status

# 查看插件信息
python main.py info <plugin-id>

# 配置设置
python main.py config --token <token> --interval 12

# 启动守护进程
python main.py daemon
```

## 目录结构

同步完成后，你的目录结构如下：

```
obsidian-plugins-updating/
├── plugins/                 # 下载的插件文件
│   ├── dataview/           # 每个插件一个目录
│   │   ├── manifest.json   # 插件清单
│   │   ├── main.js         # 主程序
│   │   └── styles.css      # 样式文件（可选）
│   └── templater-obsidian/
├── logs/                   # 日志文件
├── obsidian_plugins.db     # 本地数据库
└── config.yaml            # 配置文件
```

## 故障排除

### 网络问题
- 检查网络连接
- 配置代理（如需要）：
  ```bash
  export HTTP_PROXY=http://proxy:8080
  export HTTPS_PROXY=http://proxy:8080
  ```

### API限制
- 配置GitHub Personal Access Token
- 增加请求间隔：
  ```yaml
  github:
    request_interval: 2.0  # 增加到2秒
  ```

### 下载失败
- 查看日志文件：`logs/sync.log`
- 重新运行同步命令
- 检查磁盘空间

## 下一步

- 阅读完整的 [README.md](README.md) 了解所有功能
- 根据需要调整 `config.yaml` 配置
- 设置定时任务或守护进程实现自动同步
- 集成到你的工作流程中

## 获取帮助

如果遇到问题：

1. 查看日志文件：`logs/sync.log`
2. 运行测试脚本：`python test_basic.py`
3. 检查配置文件：`config.yaml`
4. 提交Issue到项目仓库
