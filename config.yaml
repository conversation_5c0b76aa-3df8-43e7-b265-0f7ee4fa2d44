# Obsidian插件同步器配置文件

# GitHub API配置
github:
  # GitHub Personal Access Token (可选，用于提高API速率限制)
  token: ""
  # API请求间隔（秒），避免速率限制
  request_interval: 1.0
  # 最大重试次数
  max_retries: 3
  # 超时时间（秒）
  timeout: 30

# 本地存储配置
storage:
  # 插件下载目录
  plugins_dir: "./plugins"
  # 数据库文件路径
  database_path: "./obsidian_plugins.db"
  # 日志文件路径
  log_file: "./logs/sync.log"

# 同步配置
sync:
  # 自动同步间隔（小时）
  auto_sync_interval: 24
  # 是否启用自动同步
  enable_auto_sync: true
  # 并发下载数量
  max_concurrent_downloads: 5
  # 是否下载所有插件（false则只下载更新）
  download_all: false

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  max_file_size: "10MB"
  backup_count: 5
  console_output: true

# 过滤配置
filters:
  # 排除的插件ID列表
  excluded_plugins: []
  # 只包含的插件ID列表（为空则包含所有）
  # 测试用：扩展到更多知名插件
  included_plugins: ["dataview", "templater-obsidian", "obsidian-git", "table-editor-obsidian", "nldates-obsidian", "calendar", "kanban", "obsidian-mind-map", "quickadd", "obsidian-excalidraw-plugin"]
  # 最小下载量过滤
  min_downloads: 0
