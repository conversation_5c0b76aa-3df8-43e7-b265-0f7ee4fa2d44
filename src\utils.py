"""工具函数模块"""
import os
import hashlib
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from .logger import logger


def calculate_file_hash(file_path: Path, algorithm: str = "sha256") -> str:
    """计算文件哈希值"""
    hash_func = hashlib.new(algorithm)
    
    try:
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_func.update(chunk)
        return hash_func.hexdigest()
    except Exception as e:
        logger.error(f"计算文件哈希失败 {file_path}: {e}")
        return ""


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def format_duration(seconds: float) -> str:
    """格式化时间间隔"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"


def safe_json_load(file_path: Path) -> Optional[Dict[str, Any]]:
    """安全加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载JSON文件失败 {file_path}: {e}")
        return None


def safe_json_save(data: Dict[str, Any], file_path: Path) -> bool:
    """安全保存JSON文件"""
    try:
        file_path.parent.mkdir(parents=True, exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存JSON文件失败 {file_path}: {e}")
        return False


def cleanup_old_files(directory: Path, max_age_days: int = 30) -> int:
    """清理旧文件"""
    if not directory.exists():
        return 0
    
    cutoff_time = datetime.now() - timedelta(days=max_age_days)
    cleaned_count = 0
    
    try:
        for file_path in directory.rglob("*"):
            if file_path.is_file():
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_time:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                        logger.debug(f"删除旧文件: {file_path}")
                    except Exception as e:
                        logger.warning(f"删除文件失败 {file_path}: {e}")
        
        logger.info(f"清理完成，删除了 {cleaned_count} 个旧文件")
        return cleaned_count
        
    except Exception as e:
        logger.error(f"清理目录失败 {directory}: {e}")
        return 0


def validate_plugin_id(plugin_id: str) -> bool:
    """验证插件ID格式"""
    if not plugin_id:
        return False
    
    # 插件ID应该只包含字母、数字、连字符和下划线
    import re
    pattern = r'^[a-zA-Z0-9_-]+$'
    return bool(re.match(pattern, plugin_id))


def sanitize_filename(filename: str) -> str:
    """清理文件名，移除非法字符"""
    import re
    # 移除或替换非法字符
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 移除前后空格和点
    sanitized = sanitized.strip(' .')
    # 限制长度
    if len(sanitized) > 255:
        sanitized = sanitized[:255]
    
    return sanitized or "unnamed"


def get_directory_size(directory: Path) -> int:
    """获取目录大小"""
    total_size = 0
    try:
        for file_path in directory.rglob("*"):
            if file_path.is_file():
                total_size += file_path.stat().st_size
    except Exception as e:
        logger.error(f"计算目录大小失败 {directory}: {e}")
    
    return total_size


def create_backup(source_path: Path, backup_dir: Path) -> Optional[Path]:
    """创建文件备份"""
    try:
        backup_dir.mkdir(parents=True, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{source_path.stem}_{timestamp}{source_path.suffix}"
        backup_path = backup_dir / backup_name
        
        import shutil
        if source_path.is_file():
            shutil.copy2(source_path, backup_path)
        elif source_path.is_dir():
            shutil.copytree(source_path, backup_path)
        else:
            return None
        
        logger.info(f"创建备份: {backup_path}")
        return backup_path
        
    except Exception as e:
        logger.error(f"创建备份失败 {source_path}: {e}")
        return None


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}")
                        await asyncio.sleep(delay * (2 ** attempt))  # 指数退避
                    else:
                        logger.error(f"函数 {func.__name__} 所有重试都失败")
            
            raise last_exception
        
        return wrapper
    return decorator


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total: int, description: str = "Processing"):
        self.total = total
        self.current = 0
        self.description = description
        self.start_time = datetime.now()
    
    def update(self, increment: int = 1):
        """更新进度"""
        self.current += increment
        self._log_progress()
    
    def _log_progress(self):
        """记录进度"""
        if self.total == 0:
            percentage = 100
        else:
            percentage = (self.current / self.total) * 100
        
        elapsed = (datetime.now() - self.start_time).total_seconds()
        if self.current > 0 and elapsed > 0:
            rate = self.current / elapsed
            eta = (self.total - self.current) / rate if rate > 0 else 0
            eta_str = format_duration(eta)
        else:
            eta_str = "未知"
        
        logger.info(f"{self.description}: {self.current}/{self.total} "
                   f"({percentage:.1f}%) - 预计剩余时间: {eta_str}")
    
    def finish(self):
        """完成进度"""
        self.current = self.total
        elapsed = (datetime.now() - self.start_time).total_seconds()
        logger.info(f"{self.description} 完成，总耗时: {format_duration(elapsed)}")


# 导入asyncio用于重试装饰器
import asyncio
