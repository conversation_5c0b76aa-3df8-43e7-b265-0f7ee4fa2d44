#!/usr/bin/env python3
"""Obsidian插件同步器主程序"""
import asyncio
import click
import signal
import sys
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.text import Text

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.plugin_manager import PluginManager
from src.scheduler import scheduler
from src.config import config, save_config
from src.logger import logger
from src.utils import format_duration, format_file_size
from src.web_server import web_server
from src.analytics import analytics

console = Console()


@click.group()
@click.version_option(version="1.0.0")
def cli():
    """Obsidian社区插件同步工具
    
    自动同步和管理Obsidian社区插件市场的所有插件。
    """
    pass


@cli.command()
@click.option('--all', 'sync_all', is_flag=True, help='下载所有插件（包括已存在的）')
@click.option('--force', is_flag=True, help='强制重新下载')
def sync(sync_all, force):
    """同步所有插件"""
    if sync_all:
        config.sync.download_all = True
    
    console.print("[bold blue]开始同步Obsidian插件...[/bold blue]")
    
    async def run_sync():
        manager = PluginManager()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("正在同步插件...", total=None)
            
            stats = await manager.sync_all_plugins()
            
            progress.update(task, description="同步完成")
        
        # 显示结果
        _display_sync_results(stats)
    
    try:
        asyncio.run(run_sync())
    except KeyboardInterrupt:
        console.print("\n[yellow]同步被用户中断[/yellow]")
    except Exception as e:
        console.print(f"[red]同步失败: {e}[/red]")


@cli.command()
def update():
    """更新过期插件"""
    console.print("[bold blue]检查插件更新...[/bold blue]")
    
    async def run_update():
        manager = PluginManager()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("正在更新插件...", total=None)
            
            stats = await manager.update_outdated_plugins()
            
            progress.update(task, description="更新完成")
        
        _display_sync_results(stats)
    
    try:
        asyncio.run(run_update())
    except KeyboardInterrupt:
        console.print("\n[yellow]更新被用户中断[/yellow]")
    except Exception as e:
        console.print(f"[red]更新失败: {e}[/red]")


@cli.command()
def status():
    """显示同步状态和统计信息"""
    manager = PluginManager()
    
    # 获取统计信息
    stats = manager.get_plugin_statistics()
    sync_history = manager.get_sync_history(5)
    scheduler_status = scheduler.get_status()
    
    # 显示调度器状态
    console.print(Panel.fit(
        f"运行状态: {'🟢 运行中' if scheduler_status['running'] else '🔴 已停止'}\n"
        f"自动同步: {'✅ 启用' if scheduler_status['auto_sync_enabled'] else '❌ 禁用'}\n"
        f"同步间隔: {scheduler_status['sync_interval_hours']} 小时\n"
        f"上次同步: {scheduler_status['last_sync_time'] or '从未同步'}\n"
        f"下次同步: {scheduler_status['next_sync_time'] or '未计划'}",
        title="调度器状态",
        border_style="blue"
    ))
    
    # 显示插件统计
    table = Table(title="插件统计")
    table.add_column("状态", style="cyan")
    table.add_column("数量", justify="right", style="magenta")
    
    table.add_row("总插件数", str(stats.get('total_plugins', 0)))
    
    by_status = stats.get('by_status', {})
    for status, count in by_status.items():
        table.add_row(status, str(count))
    
    console.print(table)
    
    # 显示同步历史
    if sync_history:
        history_table = Table(title="最近同步历史")
        history_table.add_column("时间", style="cyan")
        history_table.add_column("总数", justify="right")
        history_table.add_column("成功", justify="right", style="green")
        history_table.add_column("失败", justify="right", style="red")
        history_table.add_column("成功率", justify="right", style="blue")
        history_table.add_column("耗时", justify="right")
        
        for record in sync_history:
            history_table.add_row(
                record.sync_start_time.strftime("%Y-%m-%d %H:%M") if record.sync_start_time else "未知",
                str(record.total_plugins),
                str(record.downloaded_plugins + record.updated_plugins),
                str(record.failed_plugins),
                f"{record.success_rate:.1f}%",
                format_duration(record.duration_seconds)
            )
        
        console.print(history_table)


@cli.command()
def daemon():
    """以守护进程模式运行"""
    console.print("[bold green]启动Obsidian插件同步守护进程...[/bold green]")

    def signal_handler(signum, frame):
        console.print("\n[yellow]收到停止信号，正在关闭...[/yellow]")
        scheduler.stop()
        sys.exit(0)

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        scheduler.start()
        console.print("[green]守护进程已启动，按 Ctrl+C 停止[/green]")

        # 保持程序运行
        while scheduler.running:
            import time
            time.sleep(1)

    except KeyboardInterrupt:
        console.print("\n[yellow]守护进程被用户中断[/yellow]")
    finally:
        scheduler.stop()


@cli.command()
@click.option('--host', default='127.0.0.1', help='Web服务器主机地址')
@click.option('--port', default=8080, help='Web服务器端口')
def web(host, port):
    """启动Web界面"""
    console.print(f"[bold green]启动Web界面服务器...[/bold green]")
    console.print(f"[blue]访问地址: http://{host}:{port}[/blue]")

    try:
        web_server.host = host
        web_server.port = port
        web_server.run()
    except KeyboardInterrupt:
        console.print("\n[yellow]Web服务器被用户中断[/yellow]")
    except Exception as e:
        console.print(f"[red]Web服务器启动失败: {e}[/red]")


@cli.command()
@click.option('--token', help='GitHub Personal Access Token')
@click.option('--interval', type=int, help='自动同步间隔（小时）')
@click.option('--enable-auto', is_flag=True, help='启用自动同步')
@click.option('--disable-auto', is_flag=True, help='禁用自动同步')
def config_cmd(token, interval, enable_auto, disable_auto):
    """配置设置"""
    if token:
        config.github.token = token
        console.print("[green]GitHub token已更新[/green]")
    
    if interval:
        config.sync.auto_sync_interval = interval
        console.print(f"[green]同步间隔已设置为 {interval} 小时[/green]")
    
    if enable_auto:
        config.sync.enable_auto_sync = True
        console.print("[green]自动同步已启用[/green]")
    
    if disable_auto:
        config.sync.enable_auto_sync = False
        console.print("[yellow]自动同步已禁用[/yellow]")
    
    # 保存配置
    save_config(config)
    console.print("[blue]配置已保存[/blue]")


@cli.command()
@click.argument('plugin_id')
def info(plugin_id):
    """显示插件详细信息"""
    manager = PluginManager()
    plugin = manager.db.get_plugin(plugin_id)
    
    if not plugin:
        console.print(f"[red]未找到插件: {plugin_id}[/red]")
        return
    
    # 显示插件信息
    info_text = f"""
[bold]插件ID:[/bold] {plugin.id}
[bold]名称:[/bold] {plugin.name}
[bold]作者:[/bold] {plugin.author}
[bold]描述:[/bold] {plugin.description}
[bold]仓库:[/bold] {plugin.repo}
[bold]当前版本:[/bold] {plugin.current_version or '未下载'}
[bold]最新版本:[/bold] {plugin.latest_version or '未知'}
[bold]状态:[/bold] {plugin.status.value}
[bold]本地路径:[/bold] {plugin.local_path or '无'}
[bold]最后更新:[/bold] {plugin.last_updated or '从未'}
[bold]最后检查:[/bold] {plugin.last_checked or '从未'}
"""
    
    if plugin.error_message:
        info_text += f"[bold red]错误信息:[/bold red] {plugin.error_message}"
    
    console.print(Panel(info_text.strip(), title=f"插件信息: {plugin.name}"))


@cli.command()
@click.option('--format', 'export_format', default='json', type=click.Choice(['json', 'csv', 'excel']), help='导出格式')
@click.option('--output', help='输出文件路径')
def export(export_format, output):
    """导出插件数据"""
    console.print(f"[blue]正在导出插件数据为 {export_format.upper()} 格式...[/blue]")

    try:
        output_file = analytics.export_plugin_data(export_format, output)
        console.print(f"[green]数据已导出到: {output_file}[/green]")
    except Exception as e:
        console.print(f"[red]导出失败: {e}[/red]")


@cli.command()
@click.option('--output', help='报告输出路径')
def report(output):
    """生成插件分析报告"""
    console.print("[blue]正在生成插件分析报告...[/blue]")

    try:
        report_file = analytics.generate_report(output)
        console.print(f"[green]分析报告已生成: {report_file}[/green]")

        # 显示简要统计
        rankings = analytics.get_plugin_rankings(10)
        author_analysis = analytics.get_author_analysis()

        console.print("\n[bold]📊 简要统计:[/bold]")
        console.print(f"总作者数: {author_analysis.get('total_authors', 0)}")
        console.print(f"独立作者: {author_analysis.get('single_plugin_authors', 0)}")
        console.print(f"最新插件: {len(rankings.get('newest', []))}")
        console.print(f"最近更新: {len(rankings.get('recently_updated', []))}")

    except Exception as e:
        console.print(f"[red]生成报告失败: {e}[/red]")


@cli.command()
@click.option('--limit', default=20, help='显示数量限制')
@click.option('--category', type=click.Choice(['newest', 'updated', 'downloads', 'authors']), help='排行榜类别')
def rankings(limit, category):
    """显示插件排行榜"""
    console.print("[blue]正在获取插件排行榜...[/blue]")

    try:
        data = analytics.get_plugin_rankings(limit)

        if category == 'newest' or not category:
            console.print(f"\n[bold green]🆕 最新上架插件 (Top {limit})[/bold green]")
            table = Table()
            table.add_column("排名", style="cyan", width=6)
            table.add_column("插件名称", style="green")
            table.add_column("作者", style="blue")
            table.add_column("仓库", style="magenta")
            table.add_column("上架时间", style="yellow")

            for i, plugin in enumerate(data.get('newest', [])[:limit], 1):
                table.add_row(
                    str(i),
                    plugin['name'],
                    plugin['author'],
                    plugin['repo'],
                    plugin.get('created_at', '未知')[:10] if plugin.get('created_at') else '未知'
                )

            console.print(table)

        if category == 'updated' or not category:
            console.print(f"\n[bold blue]🔄 最近更新插件 (Top {limit})[/bold blue]")
            table = Table()
            table.add_column("排名", style="cyan", width=6)
            table.add_column("插件名称", style="green")
            table.add_column("作者", style="blue")
            table.add_column("当前版本", style="magenta")
            table.add_column("更新时间", style="yellow")

            for i, plugin in enumerate(data.get('recently_updated', [])[:limit], 1):
                table.add_row(
                    str(i),
                    plugin['name'],
                    plugin['author'],
                    plugin.get('current_version', '未知'),
                    plugin.get('last_updated', '未知')[:10] if plugin.get('last_updated') else '未知'
                )

            console.print(table)

        if category == 'authors' or not category:
            console.print(f"\n[bold purple]👥 高产作者排行 (Top {limit})[/bold purple]")
            table = Table()
            table.add_column("排名", style="cyan", width=6)
            table.add_column("作者", style="green")
            table.add_column("插件数量", style="blue", justify="right")
            table.add_column("总下载量", style="magenta", justify="right")
            table.add_column("最近活动", style="yellow")

            for i, author in enumerate(data.get('top_authors', [])[:limit], 1):
                table.add_row(
                    str(i),
                    author['author'],
                    str(author['plugin_count']),
                    f"{author.get('total_downloads', 0):,}",
                    author.get('latest_update', '未知')[:10] if author.get('latest_update') else '未知'
                )

            console.print(table)

    except Exception as e:
        console.print(f"[red]获取排行榜失败: {e}[/red]")


@cli.command()
def categories():
    """显示插件类别分析"""
    console.print("[blue]正在分析插件类别...[/blue]")

    try:
        data = analytics.get_plugin_categories()

        console.print("\n[bold]📂 插件类别分布[/bold]")

        table = Table()
        table.add_column("类别", style="cyan")
        table.add_column("插件数量", style="green", justify="right")
        table.add_column("热门插件", style="blue")

        for category, info in data.get('categories', {}).items():
            top_plugins = ', '.join([p['name'] for p in info['plugins'][:3]])
            table.add_row(
                category,
                str(info['count']),
                top_plugins
            )

        console.print(table)

        uncategorized_count = data.get('uncategorized_count', 0)
        if uncategorized_count > 0:
            console.print(f"\n[yellow]未分类插件: {uncategorized_count} 个[/yellow]")

    except Exception as e:
        console.print(f"[red]类别分析失败: {e}[/red]")


def _display_sync_results(stats):
    """显示同步结果"""
    # 创建结果面板
    result_text = Text()
    result_text.append("同步完成!\n\n", style="bold green")
    result_text.append(f"总插件数: {stats.total_plugins}\n")
    result_text.append(f"下载成功: {stats.downloaded_plugins}\n", style="green")
    result_text.append(f"更新成功: {stats.updated_plugins}\n", style="blue")
    result_text.append(f"失败数量: {stats.failed_plugins}\n", style="red")
    result_text.append(f"成功率: {stats.success_rate:.1f}%\n", style="cyan")
    result_text.append(f"总耗时: {format_duration(stats.duration_seconds)}", style="yellow")
    
    console.print(Panel(result_text, title="同步结果", border_style="green"))


if __name__ == "__main__":
    cli()
