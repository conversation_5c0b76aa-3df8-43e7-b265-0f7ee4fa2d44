# Obsidian插件同步器配置文件示例
# 复制此文件为 config.yaml 并根据需要修改

# GitHub API配置
github:
  # GitHub Personal Access Token (可选，用于提高API速率限制)
  # 获取方式: GitHub Settings -> Developer settings -> Personal access tokens
  # 权限: public_repo (如果只访问公共仓库)
  token: ""
  
  # API请求间隔（秒），避免速率限制
  # 建议值: 0.5-2.0秒
  request_interval: 1.0
  
  # 最大重试次数
  max_retries: 3
  
  # 超时时间（秒）
  timeout: 30

# 本地存储配置
storage:
  # 插件下载目录
  plugins_dir: "./plugins"
  
  # 数据库文件路径
  database_path: "./obsidian_plugins.db"
  
  # 日志文件路径
  log_file: "./logs/sync.log"

# 同步配置
sync:
  # 自动同步间隔（小时）
  # 建议值: 12-24小时
  auto_sync_interval: 24
  
  # 是否启用自动同步
  enable_auto_sync: true
  
  # 并发下载数量
  # 建议值: 3-10，取决于网络和API限制
  max_concurrent_downloads: 5
  
  # 是否下载所有插件（false则只下载更新）
  download_all: false

# 日志配置
logging:
  # 日志级别: DEBUG, INFO, WARNING, ERROR
  level: "INFO"
  
  # 单个日志文件最大大小
  max_file_size: "10MB"
  
  # 保留的日志文件数量
  backup_count: 5
  
  # 是否在控制台输出日志
  console_output: true

# 过滤配置
filters:
  # 排除的插件ID列表
  # 示例: ["plugin-id-1", "plugin-id-2"]
  excluded_plugins: []
  
  # 只包含的插件ID列表（为空则包含所有）
  # 示例: ["dataview", "templater-obsidian", "obsidian-git"]
  included_plugins: []
  
  # 最小下载量过滤（过滤掉下载量太少的插件）
  min_downloads: 0

# 高级配置（一般不需要修改）
advanced:
  # 清理旧文件的天数
  cleanup_days: 30
  
  # 是否启用文件完整性检查
  verify_files: true
  
  # 是否创建备份
  create_backups: false
