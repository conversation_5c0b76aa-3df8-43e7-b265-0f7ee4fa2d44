"""同步调度器模块"""
import asyncio
import schedule
import time
from datetime import datetime, timedelta
from typing import Optional, Callable, Any
from threading import Thread, Event
from .plugin_manager import PluginManager
from .config import config
from .logger import logger


class SyncScheduler:
    """同步调度器"""
    
    def __init__(self):
        self.plugin_manager = PluginManager()
        self.running = False
        self.stop_event = Event()
        self.scheduler_thread: Optional[Thread] = None
        self.last_sync_time: Optional[datetime] = None
        
    def start(self):
        """启动调度器"""
        if self.running:
            logger.warning("调度器已经在运行")
            return
        
        self.running = True
        self.stop_event.clear()
        
        # 设置定时任务
        if config.sync.enable_auto_sync:
            schedule.every(config.sync.auto_sync_interval).hours.do(self._schedule_sync)
            logger.info(f"已设置自动同步，间隔: {config.sync.auto_sync_interval} 小时")
        
        # 启动调度线程
        self.scheduler_thread = Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("同步调度器已启动")
    
    def stop(self):
        """停止调度器"""
        if not self.running:
            return
        
        self.running = False
        self.stop_event.set()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        schedule.clear()
        logger.info("同步调度器已停止")
    
    def _run_scheduler(self):
        """运行调度器主循环"""
        while self.running and not self.stop_event.is_set():
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"调度器运行错误: {e}")
                time.sleep(5)
    
    def _schedule_sync(self):
        """调度同步任务"""
        if not self.running:
            return
        
        logger.info("开始执行定时同步任务")
        
        # 在新的事件循环中运行异步任务
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            stats = loop.run_until_complete(self.plugin_manager.sync_all_plugins())
            self.last_sync_time = datetime.now()
            
            logger.info(f"定时同步完成: 成功 {stats.downloaded_plugins}, "
                       f"失败 {stats.failed_plugins}, 成功率 {stats.success_rate:.1f}%")
            
        except Exception as e:
            logger.error(f"定时同步失败: {e}")
        finally:
            loop.close()
    
    async def manual_sync(self) -> Any:
        """手动同步"""
        logger.info("开始手动同步")
        stats = await self.plugin_manager.sync_all_plugins()
        self.last_sync_time = datetime.now()
        return stats
    
    async def manual_update(self) -> Any:
        """手动更新"""
        logger.info("开始手动更新")
        stats = await self.plugin_manager.update_outdated_plugins()
        return stats
    
    def get_next_sync_time(self) -> Optional[datetime]:
        """获取下次同步时间"""
        if not config.sync.enable_auto_sync or not self.last_sync_time:
            return None
        
        return self.last_sync_time + timedelta(hours=config.sync.auto_sync_interval)
    
    def get_status(self) -> dict:
        """获取调度器状态"""
        return {
            "running": self.running,
            "auto_sync_enabled": config.sync.enable_auto_sync,
            "sync_interval_hours": config.sync.auto_sync_interval,
            "last_sync_time": self.last_sync_time,
            "next_sync_time": self.get_next_sync_time(),
            "pending_jobs": len(schedule.jobs)
        }


class TaskQueue:
    """任务队列管理器"""
    
    def __init__(self, max_workers: int = 3):
        self.max_workers = max_workers
        self.queue = asyncio.Queue()
        self.workers = []
        self.running = False
    
    async def start(self):
        """启动任务队列"""
        if self.running:
            return
        
        self.running = True
        self.workers = [
            asyncio.create_task(self._worker(f"worker-{i}"))
            for i in range(self.max_workers)
        ]
        logger.info(f"任务队列已启动，工作线程数: {self.max_workers}")
    
    async def stop(self):
        """停止任务队列"""
        if not self.running:
            return
        
        self.running = False
        
        # 等待队列清空
        await self.queue.join()
        
        # 取消所有工作线程
        for worker in self.workers:
            worker.cancel()
        
        # 等待工作线程结束
        await asyncio.gather(*self.workers, return_exceptions=True)
        
        logger.info("任务队列已停止")
    
    async def add_task(self, coro: Callable, *args, **kwargs):
        """添加任务到队列"""
        await self.queue.put((coro, args, kwargs))
    
    async def _worker(self, name: str):
        """工作线程"""
        logger.debug(f"工作线程 {name} 已启动")
        
        while self.running:
            try:
                # 获取任务
                coro, args, kwargs = await asyncio.wait_for(
                    self.queue.get(), timeout=1.0
                )
                
                # 执行任务
                try:
                    if asyncio.iscoroutinefunction(coro):
                        await coro(*args, **kwargs)
                    else:
                        coro(*args, **kwargs)
                except Exception as e:
                    logger.error(f"任务执行失败 ({name}): {e}")
                finally:
                    self.queue.task_done()
                    
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"工作线程 {name} 错误: {e}")
        
        logger.debug(f"工作线程 {name} 已停止")


# 全局调度器实例
scheduler = SyncScheduler()
