# 数据分析功能指南

## 📊 概述

Obsidian插件同步器内置了强大的数据分析功能，可以帮你深入了解Obsidian插件生态系统。所有数据都存储在本地SQLite数据库中，支持多种分析和导出方式。

## 🚀 快速开始

### 1. 基础分析
```bash
# 运行快速分析脚本
python analyze_plugins.py
```

这将显示：
- 基础统计信息
- 最新上架插件
- 最近更新插件
- 高产作者排行
- 插件类别分布
- 作者分布分析
- 最近30天活动统计

### 2. 命令行分析工具

#### 插件排行榜
```bash
# 查看所有排行榜
python main.py rankings

# 只看最新插件
python main.py rankings --category newest --limit 20

# 只看高产作者
python main.py rankings --category authors --limit 15
```

#### 插件类别分析
```bash
python main.py categories
```

#### 生成详细报告
```bash
# 生成Markdown格式的完整分析报告
python main.py report

# 指定输出路径
python main.py report --output my_report.md
```

#### 数据导出
```bash
# 导出为JSON格式
python main.py export --format json

# 导出为CSV格式
python main.py export --format csv

# 导出为Excel格式
python main.py export --format excel

# 指定输出文件
python main.py export --format json --output plugins_data.json
```

### 3. 数据库查询示例
```bash
# 运行预定义的查询示例
python query_examples.py
```

## 📈 分析功能详解

### 1. 插件排行榜

#### 最新上架插件
- 按创建时间排序的最新插件
- 显示插件名称、作者、仓库链接
- 帮助发现新兴插件

#### 最近更新插件
- 按更新时间排序的活跃插件
- 显示版本信息和更新时间
- 了解插件维护状态

#### 下载量排行
- 按下载量排序的热门插件（如果有数据）
- 反映插件的受欢迎程度

#### 高产作者排行
- 按插件数量排序的作者
- 显示总下载量和最近活动
- 发现活跃的开发者

### 2. 插件类别分析

系统会根据插件名称和描述自动分类：

- **笔记管理**: 笔记、日记、知识管理相关
- **编辑器增强**: 编辑功能、格式化、Markdown增强
- **任务管理**: 待办事项、GTD、看板、项目管理
- **数据可视化**: 图表、图形、图表、可视化
- **模板系统**: 模板、代码片段、样板文件
- **链接管理**: 链接、反向链接、引用、引文
- **同步备份**: 同步、备份、导出、导入、Git
- **主题美化**: 主题、样式、CSS、外观、UI
- **搜索增强**: 搜索、查找、查询、过滤
- **时间管理**: 时间、日历、日程、提醒
- **学习工具**: 学习、闪卡、间隔重复、Anki
- **开发工具**: 代码、编程、开发者、API

### 3. 作者分析

#### 作者分布
- 独立作者（只有1个插件）
- 小规模作者（2-3个插件）
- 中等规模作者（4-5个插件）
- 活跃作者（6-10个插件）
- 高产作者（10个以上插件）

#### 高产作者特征
- 插件数量统计
- 总下载量（如果有数据）
- 最近活动时间
- 维护活跃度

### 4. 时间线分析

#### 活动趋势
- 每日新增插件数量
- 每日更新插件数量
- 平均活跃度统计
- 生态系统增长趋势

#### 周期性分析
- 可配置分析周期（默认30天）
- 识别活跃期和平静期
- 预测未来趋势

## 🔍 高级查询

### 直接数据库访问

数据库文件位置：`./obsidian_plugins.db`

```bash
# 使用SQLite命令行工具
sqlite3 obsidian_plugins.db

# 查看表结构
.schema plugins

# 导出为CSV
.mode csv
.output plugins.csv
SELECT * FROM plugins;
```

### 常用SQL查询

#### 1. 查找特定作者的所有插件
```sql
SELECT name, current_version, status, last_updated
FROM plugins 
WHERE author = 'author_name'
ORDER BY name;
```

#### 2. 查找包含关键词的插件
```sql
SELECT name, author, description
FROM plugins 
WHERE name LIKE '%keyword%' OR description LIKE '%keyword%'
ORDER BY name;
```

#### 3. 查找需要更新的插件
```sql
SELECT name, author, current_version, latest_version
FROM plugins 
WHERE current_version != latest_version
AND current_version IS NOT NULL 
AND latest_version IS NOT NULL;
```

#### 4. 统计每个状态的插件数量
```sql
SELECT status, COUNT(*) as count
FROM plugins 
GROUP BY status 
ORDER BY count DESC;
```

#### 5. 查找最近活跃的插件
```sql
SELECT name, author, last_updated
FROM plugins 
WHERE last_updated >= date('now', '-30 days')
ORDER BY last_updated DESC;
```

## 📊 Web界面分析

Web界面也提供了数据分析功能：

```bash
# 启动Web界面
python start_web.py
```

Web界面分析功能：
- 实时统计图表
- 交互式排行榜
- 插件搜索和过滤
- 导出功能
- 历史趋势图

## 📁 导出格式说明

### JSON格式
- 完整的结构化数据
- 适合程序处理
- 包含所有字段信息

### CSV格式
- 表格形式的数据
- 适合Excel等工具分析
- 便于数据透视表分析

### Excel格式
- 原生Excel文件
- 支持复杂的数据分析
- 可以添加图表和公式

## 🔧 自定义分析

### 扩展分析脚本

你可以基于现有的分析模块创建自定义分析：

```python
from src.analytics import analytics
from src.database import PluginDatabase

# 获取数据
db = PluginDatabase()
plugins = db.get_all_plugins()

# 自定义分析逻辑
# ...

# 使用内置分析功能
rankings = analytics.get_plugin_rankings(100)
categories = analytics.get_plugin_categories()
```

### 添加新的分析维度

可以扩展 `src/analytics.py` 文件，添加新的分析功能：

1. 插件大小分析
2. 依赖关系分析
3. 更新频率分析
4. 错误率统计
5. 地理分布分析（基于作者信息）

## 💡 分析技巧

### 1. 发现趋势
- 定期运行分析，比较不同时期的数据
- 关注新兴类别和热门插件
- 跟踪高产作者的新作品

### 2. 质量评估
- 结合下载量和更新频率评估插件质量
- 关注维护活跃的插件
- 避免长期未更新的插件

### 3. 生态洞察
- 分析插件类别分布了解用户需求
- 观察作者活跃度了解社区健康度
- 通过时间线分析预测发展趋势

## 🚨 注意事项

1. **数据准确性**: 分析结果基于同步时的数据，建议定期同步
2. **下载量数据**: GitHub API的下载量数据可能不完整
3. **分类准确性**: 自动分类基于关键词，可能存在误分类
4. **性能考虑**: 大量数据分析可能需要较长时间

## 📚 相关文档

- [README.md](README.md) - 主要文档
- [WEB_INTERFACE.md](WEB_INTERFACE.md) - Web界面指南
- [QUICKSTART.md](QUICKSTART.md) - 快速开始指南
