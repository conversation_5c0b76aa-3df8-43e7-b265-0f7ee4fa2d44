<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-card {
            transition: transform 0.2s;
        }
        .status-card:hover {
            transform: translateY(-2px);
        }
        .plugin-card {
            border-left: 4px solid #007bff;
        }
        .plugin-card.downloaded {
            border-left-color: #28a745;
        }
        .plugin-card.error {
            border-left-color: #dc3545;
        }
        .plugin-card.updated {
            border-left-color: #17a2b8;
        }
        .loading {
            display: none;
        }
        .loading.show {
            display: inline-block;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-dark bg-dark">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="bi bi-cloud-download"></i>
                Obsidian插件同步器
            </span>
            <div class="d-flex">
                <button class="btn btn-outline-light me-2" onclick="refreshData()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
                <div class="dropdown">
                    <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-gear"></i> 操作
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="startSync()">
                            <i class="bi bi-download"></i> 开始同步
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="startUpdate()">
                            <i class="bi bi-arrow-up-circle"></i> 更新插件
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="toggleScheduler()">
                            <i class="bi bi-play-circle"></i> 调度器控制
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 状态卡片 -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card status-card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">官方总数</h6>
                                <h3 id="official-total">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-cloud fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card status-card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">本地总数</h6>
                                <h3 id="local-total">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-hdd fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card status-card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">已下载</h6>
                                <h3 id="downloaded-plugins">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-check-circle fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card status-card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">失败数量</h6>
                                <h3 id="failed-plugins">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-exclamation-triangle fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card status-card bg-warning text-dark">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">同步进度</h6>
                                <h3 id="sync-progress">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-percent fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card status-card bg-secondary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">调度器</h6>
                                <h6 id="scheduler-status">-</h6>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-clock fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 同步进度条 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">同步进度</h6>
                        <div class="progress mb-2" style="height: 25px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 id="progress-bar" role="progressbar" style="width: 0%">
                                <span id="progress-text">0%</span>
                            </div>
                        </div>
                        <small class="text-muted">
                            <span id="progress-detail">等待数据加载...</span>
                            <span class="float-end">最后更新: <span id="last-update-time">-</span></span>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 失败插件提醒 -->
        <div class="row mb-4" id="failed-plugins-alert" style="display: none;">
            <div class="col-12">
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <h6 class="alert-heading"><i class="bi bi-exclamation-triangle"></i> 发现失败的插件</h6>
                    <div id="failed-plugins-list"></div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>

        <!-- 插件列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">插件列表</h5>
                            <div class="d-flex">
                                <button class="btn btn-outline-success me-2" onclick="refreshOfficialCount()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新官方数据
                                </button>
                                <button class="btn btn-outline-info me-2" onclick="downloadAllPlugins()">
                                    <i class="bi bi-download"></i> 打包下载
                                </button>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex">
                                <input type="text" class="form-control me-2" id="search-input" placeholder="搜索插件..." style="width: 200px;">
                                <select class="form-select me-2" id="status-filter" style="width: 150px;">
                                    <option value="">所有状态</option>
                                    <option value="downloaded">已下载</option>
                                    <option value="updated">已更新</option>
                                    <option value="error">错误</option>
                                    <option value="available">可用</option>
                                </select>
                                <button class="btn btn-outline-primary me-2" onclick="loadPlugins()">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="view-mode" id="list-view" checked>
                                <label class="btn btn-outline-secondary" for="list-view">
                                    <i class="bi bi-list"></i> 列表
                                </label>
                                <input type="radio" class="btn-check" name="view-mode" id="card-view">
                                <label class="btn btn-outline-secondary" for="card-view">
                                    <i class="bi bi-grid"></i> 卡片
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="plugins-loading" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                        <div id="plugins-container" style="display: none;">
                            <div id="plugins-list"></div>
                            <nav aria-label="插件分页">
                                <ul class="pagination justify-content-center" id="pagination"></ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 插件详情模态框 -->
    <div class="modal fade" id="pluginModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="pluginModalTitle">插件详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="pluginModalBody">
                    <!-- 插件详情内容 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/dashboard.js"></script>
</body>
</html>
