#!/usr/bin/env python3
"""快速启动脚本"""
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 检查依赖
    try:
        import click
        import aiohttp
        import rich
    except ImportError:
        print("正在安装依赖...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    
    # 运行主程序
    from main import cli
    cli()

if __name__ == "__main__":
    main()
