"""插件信息获取模块"""
import asyncio
import aiohttp
import json
from typing import List, Optional, Dict, Any
from datetime import datetime
from .models import PluginInfo, PluginManifest, GitHubRelease, APIRateLimit
from .config import config
from .logger import logger


class PluginFetcher:
    """插件信息获取器"""
    
    COMMUNITY_PLUGINS_URL = "https://raw.githubusercontent.com/obsidianmd/obsidian-releases/master/community-plugins.json"
    GITHUB_API_BASE = "https://api.github.com"
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limit: Optional[APIRateLimit] = None
        self.headers = {
            "User-Agent": "Obsidian-Plugin-Sync/1.0.0",
            "Accept": "application/vnd.github.v3+json"
        }
        
        # 添加GitHub token（如果配置了）
        if config.github.token:
            self.headers["Authorization"] = f"token {config.github.token}"
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        timeout = aiohttp.ClientTimeout(total=config.github.timeout)
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=timeout
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def get_community_plugins(self) -> List[PluginInfo]:
        """获取社区插件列表"""
        logger.info("正在获取社区插件列表...")
        
        try:
            async with self.session.get(self.COMMUNITY_PLUGINS_URL) as response:
                response.raise_for_status()
                # 强制指定内容类型为JSON
                text = await response.text()
                import json
                data = json.loads(text)
                
                plugins = []
                for item in data:
                    try:
                        plugin = PluginInfo(**item)
                        plugins.append(plugin)
                    except Exception as e:
                        logger.warning(f"解析插件信息失败: {item.get('id', 'unknown')} - {e}")
                
                logger.info(f"成功获取 {len(plugins)} 个插件信息")
                return plugins
                
        except Exception as e:
            logger.error(f"获取社区插件列表失败: {e}")
            raise
    
    async def get_plugin_manifest(self, repo: str) -> Optional[PluginManifest]:
        """获取插件清单文件"""
        manifest_url = f"https://raw.githubusercontent.com/{repo}/master/manifest.json"
        
        try:
            await self._check_rate_limit()
            
            async with self.session.get(manifest_url) as response:
                if response.status == 404:
                    logger.warning(f"插件 {repo} 的manifest.json不存在")
                    return None

                response.raise_for_status()
                # 强制指定内容类型为JSON
                text = await response.text()
                import json
                data = json.loads(text)
                
                return PluginManifest(**data)
                
        except Exception as e:
            logger.error(f"获取插件 {repo} 的manifest.json失败: {e}")
            return None
    
    async def get_latest_release(self, repo: str) -> Optional[GitHubRelease]:
        """获取最新发布版本"""
        releases_url = f"{self.GITHUB_API_BASE}/repos/{repo}/releases/latest"
        
        try:
            await self._check_rate_limit()
            
            async with self.session.get(releases_url) as response:
                self._update_rate_limit(response.headers)
                
                if response.status == 404:
                    logger.warning(f"插件 {repo} 没有发布版本")
                    return None
                
                response.raise_for_status()
                data = await response.json()
                
                # 解析发布时间
                published_at = datetime.fromisoformat(
                    data['published_at'].replace('Z', '+00:00')
                )
                
                release = GitHubRelease(
                    tag_name=data['tag_name'],
                    name=data['name'],
                    published_at=published_at,
                    assets=data.get('assets', [])
                )
                
                # 构建下载URL
                release.download_url = f"https://github.com/{repo}/releases/download/{release.tag_name}"
                
                return release
                
        except Exception as e:
            logger.error(f"获取插件 {repo} 的最新版本失败: {e}")
            return None
    
    async def get_plugin_stats(self, repo: str) -> Dict[str, Any]:
        """获取插件统计信息"""
        repo_url = f"{self.GITHUB_API_BASE}/repos/{repo}"
        
        try:
            await self._check_rate_limit()
            
            async with self.session.get(repo_url) as response:
                self._update_rate_limit(response.headers)
                
                if response.status == 404:
                    return {}
                
                response.raise_for_status()
                data = await response.json()
                
                return {
                    "stars": data.get("stargazers_count", 0),
                    "forks": data.get("forks_count", 0),
                    "watchers": data.get("watchers_count", 0),
                    "open_issues": data.get("open_issues_count", 0),
                    "created_at": data.get("created_at"),
                    "updated_at": data.get("updated_at"),
                    "pushed_at": data.get("pushed_at"),
                }
                
        except Exception as e:
            logger.error(f"获取插件 {repo} 的统计信息失败: {e}")
            return {}
    
    async def _check_rate_limit(self):
        """检查API速率限制"""
        if self.rate_limit and self.rate_limit.remaining <= 1:
            wait_time = (self.rate_limit.reset_time - datetime.now()).total_seconds()
            if wait_time > 0:
                logger.warning(f"API速率限制，等待 {wait_time:.1f} 秒...")
                await asyncio.sleep(wait_time)
        
        # 请求间隔
        await asyncio.sleep(config.github.request_interval)
    
    def _update_rate_limit(self, headers: Dict[str, str]):
        """更新速率限制信息"""
        try:
            if 'x-ratelimit-limit' in headers:
                self.rate_limit = APIRateLimit(
                    limit=int(headers['x-ratelimit-limit']),
                    remaining=int(headers['x-ratelimit-remaining']),
                    reset_time=datetime.fromtimestamp(int(headers['x-ratelimit-reset'])),
                    used=int(headers.get('x-ratelimit-used', 0))
                )
                
                logger.debug(f"API速率限制: {self.rate_limit.remaining}/{self.rate_limit.limit}")
                
        except (KeyError, ValueError) as e:
            logger.debug(f"解析速率限制信息失败: {e}")


async def fetch_all_plugins() -> List[PluginInfo]:
    """获取所有插件信息的便捷函数"""
    async with PluginFetcher() as fetcher:
        return await fetcher.get_community_plugins()
