#!/usr/bin/env python3
"""数据库查询示例脚本"""
import sys
import sqlite3
from pathlib import Path
from datetime import datetime, timedelta

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import config


def execute_query(query: str, params: tuple = ()):
    """执行SQL查询"""
    db_path = Path(config.storage.database_path)
    if not db_path.exists():
        print("❌ 数据库文件不存在，请先运行同步命令")
        return []
    
    try:
        with sqlite3.connect(db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(query, params)
            return cursor.fetchall()
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return []


def print_results(results, title: str):
    """打印查询结果"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print('='*60)
    
    if not results:
        print("没有找到数据")
        return
    
    for i, row in enumerate(results, 1):
        print(f"{i:3d}. ", end="")
        for key in row.keys():
            value = row[key]
            if value is not None:
                print(f"{key}: {value} | ", end="")
        print()


def main():
    """主函数"""
    print("🔍 Obsidian插件数据库查询示例")
    print(f"数据库路径: {config.storage.database_path}")
    
    # 1. 查看数据库结构
    print("\n📋 数据库表结构:")
    schema_query = "SELECT sql FROM sqlite_master WHERE type='table'"
    schema_results = execute_query(schema_query)
    for row in schema_results:
        print(row['sql'])
    
    # 2. 基础统计查询
    print("\n📊 基础统计:")
    
    # 总插件数
    total_query = "SELECT COUNT(*) as total FROM plugins"
    total_result = execute_query(total_query)
    if total_result:
        print(f"总插件数: {total_result[0]['total']}")
    
    # 按状态统计
    status_query = "SELECT status, COUNT(*) as count FROM plugins GROUP BY status ORDER BY count DESC"
    status_results = execute_query(status_query)
    print("\n按状态统计:")
    for row in status_results:
        print(f"  {row['status']}: {row['count']} 个")
    
    # 3. 最新插件查询
    newest_query = """
        SELECT name, author, repo, created_at 
        FROM plugins 
        WHERE created_at IS NOT NULL 
        ORDER BY created_at DESC 
        LIMIT 10
    """
    newest_results = execute_query(newest_query)
    print_results(newest_results, "最新上架插件 (Top 10)")
    
    # 4. 最近更新插件查询
    updated_query = """
        SELECT name, author, current_version, last_updated 
        FROM plugins 
        WHERE last_updated IS NOT NULL 
        ORDER BY last_updated DESC 
        LIMIT 10
    """
    updated_results = execute_query(updated_query)
    print_results(updated_results, "最近更新插件 (Top 10)")
    
    # 5. 高产作者查询
    authors_query = """
        SELECT author, COUNT(*) as plugin_count, 
               SUM(download_count) as total_downloads,
               MAX(last_updated) as latest_activity
        FROM plugins 
        GROUP BY author 
        HAVING plugin_count >= 3
        ORDER BY plugin_count DESC 
        LIMIT 15
    """
    authors_results = execute_query(authors_query)
    print_results(authors_results, "高产作者 (3个以上插件)")
    
    # 6. 下载量排行（如果有数据）
    downloads_query = """
        SELECT name, author, download_count, current_version
        FROM plugins 
        WHERE download_count > 0 
        ORDER BY download_count DESC 
        LIMIT 10
    """
    downloads_results = execute_query(downloads_query)
    if downloads_results:
        print_results(downloads_results, "下载量排行 (Top 10)")
    
    # 7. 错误插件查询
    error_query = """
        SELECT name, author, error_message, last_checked
        FROM plugins 
        WHERE status = 'error' AND error_message IS NOT NULL
        ORDER BY last_checked DESC
        LIMIT 10
    """
    error_results = execute_query(error_query)
    if error_results:
        print_results(error_results, "最近错误插件")
    
    # 8. 版本不一致插件
    version_query = """
        SELECT name, author, current_version, latest_version, last_checked
        FROM plugins 
        WHERE current_version IS NOT NULL 
        AND latest_version IS NOT NULL 
        AND current_version != latest_version
        ORDER BY last_checked DESC
        LIMIT 10
    """
    version_results = execute_query(version_query)
    if version_results:
        print_results(version_results, "需要更新的插件")
    
    # 9. 特定作者的插件
    print("\n🔍 自定义查询示例:")
    print("查找特定作者的所有插件:")
    
    # 找一个有多个插件的作者作为示例
    sample_author_query = """
        SELECT author FROM plugins 
        GROUP BY author 
        HAVING COUNT(*) > 1 
        LIMIT 1
    """
    sample_author = execute_query(sample_author_query)
    
    if sample_author:
        author_name = sample_author[0]['author']
        author_plugins_query = """
            SELECT name, current_version, status, last_updated
            FROM plugins 
            WHERE author = ?
            ORDER BY name
        """
        author_plugins = execute_query(author_plugins_query, (author_name,))
        print_results(author_plugins, f"作者 '{author_name}' 的所有插件")
    
    # 10. 最近30天活动统计
    thirty_days_ago = (datetime.now() - timedelta(days=30)).isoformat()
    
    recent_activity_query = """
        SELECT 
            COUNT(CASE WHEN created_at >= ? THEN 1 END) as new_plugins,
            COUNT(CASE WHEN last_updated >= ? THEN 1 END) as updated_plugins
        FROM plugins
    """
    activity_results = execute_query(recent_activity_query, (thirty_days_ago, thirty_days_ago))
    
    if activity_results:
        row = activity_results[0]
        print(f"\n📈 最近30天活动:")
        print(f"新增插件: {row['new_plugins']} 个")
        print(f"更新插件: {row['updated_plugins']} 个")
    
    # 11. 自定义查询提示
    print(f"\n💡 自定义查询提示:")
    print("你可以直接连接SQLite数据库进行查询:")
    print(f"sqlite3 {config.storage.database_path}")
    print("\n常用查询示例:")
    print("1. 查看所有表: .tables")
    print("2. 查看表结构: .schema plugins")
    print("3. 导出CSV: .mode csv")
    print("4. 导出到文件: .output filename.csv")
    print("5. 搜索插件: SELECT * FROM plugins WHERE name LIKE '%keyword%';")
    print("6. 按仓库查找: SELECT * FROM plugins WHERE repo LIKE '%username%';")
    
    print("\n✅ 查询示例完成！")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  查询被用户中断")
    except Exception as e:
        print(f"\n\n💥 查询过程中发生错误: {e}")
        sys.exit(1)
