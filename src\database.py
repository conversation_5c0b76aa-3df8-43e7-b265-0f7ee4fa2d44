"""数据库管理模块"""
import sqlite3
import json
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime
from .models import PluginR<PERSON>ord, PluginStatus, SyncStats
from .config import config
from .logger import logger


class PluginDatabase:
    """插件数据库管理器"""
    
    def __init__(self, db_path: Optional[str] = None):
        self.db_path = Path(db_path or config.storage.database_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS plugins (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    author TEXT NOT NULL,
                    description TEXT,
                    repo TEXT NOT NULL,
                    current_version TEXT,
                    latest_version TEXT,
                    status TEXT DEFAULT 'unknown',
                    download_count INTEGER DEFAULT 0,
                    last_updated TIMESTAMP,
                    last_checked TIMESTAMP,
                    local_path TEXT,
                    manifest_data TEXT,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS sync_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sync_start_time TIMESTAMP,
                    sync_end_time TIMESTAMP,
                    total_plugins INTEGER,
                    downloaded_plugins INTEGER,
                    updated_plugins INTEGER,
                    failed_plugins INTEGER,
                    duration_seconds REAL,
                    success_rate REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_plugins_status ON plugins(status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_plugins_repo ON plugins(repo)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_plugins_updated ON plugins(last_updated)")
            
            conn.commit()
    
    def save_plugin(self, plugin: PluginRecord) -> bool:
        """保存或更新插件记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 准备数据
                manifest_json = json.dumps(plugin.manifest_data) if plugin.manifest_data else None
                
                # 处理状态值
                status_value = plugin.status.value if hasattr(plugin.status, 'value') else str(plugin.status)

                conn.execute("""
                    INSERT OR REPLACE INTO plugins (
                        id, name, author, description, repo, current_version,
                        latest_version, status, download_count, last_updated,
                        last_checked, local_path, manifest_data, error_message,
                        updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    plugin.id, plugin.name, plugin.author, plugin.description,
                    plugin.repo, plugin.current_version, plugin.latest_version,
                    status_value, plugin.download_count,
                    plugin.last_updated, plugin.last_checked, plugin.local_path,
                    manifest_json, plugin.error_message, datetime.now()
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"保存插件记录失败 {plugin.id}: {e}")
            return False
    
    def get_plugin(self, plugin_id: str) -> Optional[PluginRecord]:
        """获取插件记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    "SELECT * FROM plugins WHERE id = ?", (plugin_id,)
                )
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_plugin(row)
                return None
                
        except Exception as e:
            logger.error(f"获取插件记录失败 {plugin_id}: {e}")
            return None
    
    def get_all_plugins(self, status: Optional[PluginStatus] = None) -> List[PluginRecord]:
        """获取所有插件记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                if status:
                    cursor = conn.execute(
                        "SELECT * FROM plugins WHERE status = ? ORDER BY name",
                        (status.value,)
                    )
                else:
                    cursor = conn.execute(
                        "SELECT * FROM plugins ORDER BY name"
                    )
                
                return [self._row_to_plugin(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"获取插件列表失败: {e}")
            return []
    
    def get_outdated_plugins(self) -> List[PluginRecord]:
        """获取需要更新的插件"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM plugins 
                    WHERE current_version IS NOT NULL 
                    AND latest_version IS NOT NULL 
                    AND current_version != latest_version
                    ORDER BY name
                """)
                
                return [self._row_to_plugin(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"获取过期插件列表失败: {e}")
            return []
    
    def update_plugin_status(self, plugin_id: str, status: PluginStatus, 
                           error_message: Optional[str] = None) -> bool:
        """更新插件状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE plugins 
                    SET status = ?, error_message = ?, updated_at = ?
                    WHERE id = ?
                """, (status.value, error_message, datetime.now(), plugin_id))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"更新插件状态失败 {plugin_id}: {e}")
            return False
    
    def save_sync_stats(self, stats: SyncStats) -> bool:
        """保存同步统计"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO sync_history (
                        sync_start_time, sync_end_time, total_plugins,
                        downloaded_plugins, updated_plugins, failed_plugins,
                        duration_seconds, success_rate
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    stats.sync_start_time, stats.sync_end_time, stats.total_plugins,
                    stats.downloaded_plugins, stats.updated_plugins, stats.failed_plugins,
                    stats.duration_seconds, stats.success_rate
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"保存同步统计失败: {e}")
            return False
    
    def get_sync_history(self, limit: int = 10) -> List[SyncStats]:
        """获取同步历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM sync_history 
                    ORDER BY created_at DESC 
                    LIMIT ?
                """, (limit,))
                
                history = []
                for row in cursor.fetchall():
                    stats = SyncStats(
                        total_plugins=row['total_plugins'],
                        downloaded_plugins=row['downloaded_plugins'],
                        updated_plugins=row['updated_plugins'],
                        failed_plugins=row['failed_plugins'],
                        sync_start_time=datetime.fromisoformat(row['sync_start_time']) if row['sync_start_time'] else None,
                        sync_end_time=datetime.fromisoformat(row['sync_end_time']) if row['sync_end_time'] else None,
                        duration_seconds=row['duration_seconds']
                    )
                    history.append(stats)
                
                return history
                
        except Exception as e:
            logger.error(f"获取同步历史失败: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                stats = {}
                
                # 总插件数
                cursor = conn.execute("SELECT COUNT(*) FROM plugins")
                stats['total_plugins'] = cursor.fetchone()[0]
                
                # 按状态统计
                cursor = conn.execute("""
                    SELECT status, COUNT(*) 
                    FROM plugins 
                    GROUP BY status
                """)
                stats['by_status'] = dict(cursor.fetchall())
                
                # 最近更新时间
                cursor = conn.execute("""
                    SELECT MAX(last_updated) FROM plugins 
                    WHERE last_updated IS NOT NULL
                """)
                last_update = cursor.fetchone()[0]
                if last_update:
                    stats['last_update'] = datetime.fromisoformat(last_update)
                
                return stats
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def _row_to_plugin(self, row: sqlite3.Row) -> PluginRecord:
        """将数据库行转换为插件记录"""
        manifest_data = None
        if row['manifest_data']:
            try:
                manifest_data = json.loads(row['manifest_data'])
            except json.JSONDecodeError:
                pass
        
        # 安全处理状态枚举
        try:
            status = PluginStatus(row['status'])
        except ValueError:
            # 如果状态值不在枚举中，使用UNKNOWN
            status = PluginStatus.UNKNOWN

        return PluginRecord(
            id=row['id'],
            name=row['name'],
            author=row['author'],
            description=row['description'],
            repo=row['repo'],
            current_version=row['current_version'],
            latest_version=row['latest_version'],
            status=status,
            download_count=row['download_count'] or 0,
            last_updated=datetime.fromisoformat(row['last_updated']) if row['last_updated'] else None,
            last_checked=datetime.fromisoformat(row['last_checked']) if row['last_checked'] else None,
            local_path=row['local_path'],
            manifest_data=manifest_data,
            error_message=row['error_message']
        )
