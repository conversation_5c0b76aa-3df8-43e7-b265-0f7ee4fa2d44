"""配置管理模块"""
import os
import yaml
from pathlib import Path
from typing import Dict, Any, List
from pydantic import BaseModel, Field


class GitHubConfig(BaseModel):
    """GitHub API配置"""
    token: str = ""
    request_interval: float = 1.0
    max_retries: int = 3
    timeout: int = 30


class StorageConfig(BaseModel):
    """存储配置"""
    plugins_dir: str = "./plugins"
    database_path: str = "./obsidian_plugins.db"
    log_file: str = "./logs/sync.log"


class SyncConfig(BaseModel):
    """同步配置"""
    auto_sync_interval: int = 24
    enable_auto_sync: bool = True
    max_concurrent_downloads: int = 5
    download_all: bool = False


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = "INFO"
    max_file_size: str = "10MB"
    backup_count: int = 5
    console_output: bool = True


class FiltersConfig(BaseModel):
    """过滤配置"""
    excluded_plugins: List[str] = Field(default_factory=list)
    included_plugins: List[str] = Field(default_factory=list)
    min_downloads: int = 0


class Config(BaseModel):
    """主配置类"""
    github: GitHubConfig = Field(default_factory=GitHubConfig)
    storage: StorageConfig = Field(default_factory=StorageConfig)
    sync: SyncConfig = Field(default_factory=SyncConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    filters: FiltersConfig = Field(default_factory=FiltersConfig)


def load_config(config_path: str = "config.yaml") -> Config:
    """加载配置文件"""
    config_file = Path(config_path)
    
    if not config_file.exists():
        # 创建默认配置文件
        default_config = Config()
        save_config(default_config, config_path)
        return default_config
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        return Config(**config_data)
    except Exception as e:
        print(f"配置文件加载失败: {e}")
        return Config()


def save_config(config: Config, config_path: str = "config.yaml") -> None:
    """保存配置文件"""
    config_file = Path(config_path)
    config_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.dump(config.model_dump(), f, default_flow_style=False, allow_unicode=True)


# 全局配置实例
config = load_config()
