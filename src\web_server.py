"""Web界面服务器"""
import asyncio
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from fastapi import FastAPI, Request, HTTPException, BackgroundTasks
from fastapi.responses import HTMLResponse, JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

from .plugin_manager import PluginManager
from .scheduler import scheduler
from .database import PluginDatabase
from .models import PluginStatus, SyncStats
from .config import config
from .logger import logger
from .analytics import analytics


class WebServer:
    """Web服务器"""

    def __init__(self, host: str = "127.0.0.1", port: int = 8080):
        self.host = host
        self.port = port
        self.app = FastAPI(title="Obsidian插件同步器", version="1.0.0")
        self.plugin_manager = PluginManager()
        self.db = PluginDatabase()

        # 官方插件数量缓存
        self._official_total_cache = None
        self._official_total_cache_time = None
        self._cache_duration = 24 * 3600  # 24小时缓存
        
        # 创建必要目录
        templates_dir = Path("templates")
        static_dir = Path("static")
        templates_dir.mkdir(exist_ok=True)
        static_dir.mkdir(exist_ok=True)

        # 设置模板和静态文件
        self.templates = Jinja2Templates(directory="templates")
        
        # 挂载静态文件
        self.app.mount("/static", StaticFiles(directory="static"), name="static")
        
        self._setup_routes()

    async def _get_official_total(self, force_refresh: bool = False):
        """获取官方插件总数（带缓存）"""
        current_time = datetime.now().timestamp()

        # 检查缓存是否有效
        if (not force_refresh and
            self._official_total_cache is not None and
            self._official_total_cache_time is not None and
            current_time - self._official_total_cache_time < self._cache_duration):
            return self._official_total_cache

        # 获取新数据
        try:
            from .plugin_fetcher import PluginFetcher
            async with PluginFetcher() as fetcher:
                community_plugins = await fetcher.get_community_plugins()
                official_total = len(community_plugins)

                # 更新缓存
                self._official_total_cache = official_total
                self._official_total_cache_time = current_time

                logger.info(f"更新官方插件总数缓存: {official_total}")
                return official_total
        except Exception as e:
            logger.error(f"获取官方插件数量失败: {e}")
            # 返回缓存值或0
            return self._official_total_cache or 0

    def _setup_routes(self):
        """设置路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard(request: Request):
            """主仪表板"""
            return self.templates.TemplateResponse("dashboard.html", {
                "request": request,
                "title": "Obsidian插件同步器"
            })
        
        @self.app.get("/api/status")
        async def get_status(refresh_official: bool = False):
            """获取系统状态"""
            stats = self.plugin_manager.get_plugin_statistics()
            scheduler_status = scheduler.get_status()

            # 获取官方插件总数（使用缓存）
            official_total = await self._get_official_total(force_refresh=refresh_official)

            # 获取失败插件列表
            failed_plugins = self.db.get_all_plugins(PluginStatus.ERROR)

            # 计算同步进度
            local_total = stats.get('total_plugins', 0)
            downloaded_count = stats.get('by_status', {}).get('downloaded', 0)
            sync_progress = (downloaded_count / official_total * 100) if official_total > 0 else 0

            return {
                "scheduler": scheduler_status,
                "plugins": stats,
                "official_total": official_total,
                "local_total": local_total,
                "downloaded_count": downloaded_count,
                "failed_count": len(failed_plugins),
                "sync_progress": round(sync_progress, 2),
                "failed_plugins": [
                    {
                        "id": p.id,
                        "name": p.name,
                        "error": p.error_message,
                        "last_checked": p.last_checked.isoformat() if p.last_checked else None
                    } for p in failed_plugins[:10]  # 只显示前10个失败的
                ],
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.get("/api/plugins")
        async def get_plugins(
            status: Optional[str] = None,
            page: int = 1,
            limit: int = 50,
            search: Optional[str] = None
        ):
            """获取插件列表"""
            try:
                # 获取插件列表
                if status:
                    plugin_status = PluginStatus(status)
                    plugins = self.db.get_all_plugins(plugin_status)
                else:
                    plugins = self.db.get_all_plugins()
                
                # 搜索过滤
                if search:
                    search_lower = search.lower()
                    plugins = [
                        p for p in plugins 
                        if search_lower in p.name.lower() 
                        or search_lower in p.author.lower()
                        or search_lower in p.description.lower()
                    ]
                
                # 分页
                total = len(plugins)
                start = (page - 1) * limit
                end = start + limit
                plugins_page = plugins[start:end]
                
                # 转换插件数据，添加更多信息
                plugins_data = []
                for p in plugins_page:
                    plugin_data = p.model_dump()
                    # 添加额外的显示信息
                    plugin_data.update({
                        "github_url": f"https://github.com/{p.repo}",
                        "status_display": self._get_status_display(p.status),
                        "last_updated_display": p.last_updated.strftime("%Y-%m-%d %H:%M") if p.last_updated else "从未",
                        "last_checked_display": p.last_checked.strftime("%Y-%m-%d %H:%M") if p.last_checked else "从未",
                        "has_update": p.current_version != p.latest_version if p.current_version and p.latest_version else False,
                        "file_count": self._count_plugin_files(p.local_path) if p.local_path else 0
                    })
                    plugins_data.append(plugin_data)

                return {
                    "plugins": plugins_data,
                    "total": total,
                    "page": page,
                    "limit": limit,
                    "pages": (total + limit - 1) // limit
                }
                
            except Exception as e:
                logger.error(f"获取插件列表失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/plugins/{plugin_id}")
        async def get_plugin_detail(plugin_id: str):
            """获取插件详情"""
            plugin = self.db.get_plugin(plugin_id)
            if not plugin:
                raise HTTPException(status_code=404, detail="插件未找到")
            
            return plugin.model_dump()
        
        @self.app.post("/api/sync")
        async def start_sync(background_tasks: BackgroundTasks, sync_all: bool = False):
            """开始同步"""
            if sync_all:
                config.sync.download_all = True
            
            background_tasks.add_task(self._run_sync)
            return {"message": "同步任务已启动", "sync_all": sync_all}
        
        @self.app.post("/api/update")
        async def start_update(background_tasks: BackgroundTasks):
            """开始更新"""
            background_tasks.add_task(self._run_update)
            return {"message": "更新任务已启动"}
        
        @self.app.get("/api/sync-history")
        async def get_sync_history(limit: int = 10):
            """获取同步历史"""
            history = self.plugin_manager.get_sync_history(limit)
            return [h.model_dump() for h in history]
        
        @self.app.post("/api/scheduler/start")
        async def start_scheduler():
            """启动调度器"""
            scheduler.start()
            return {"message": "调度器已启动"}
        
        @self.app.post("/api/scheduler/stop")
        async def stop_scheduler():
            """停止调度器"""
            scheduler.stop()
            return {"message": "调度器已停止"}

        @self.app.post("/api/refresh-official")
        async def refresh_official_count():
            """手动刷新官方插件数量"""
            try:
                official_total = await self._get_official_total(force_refresh=True)
                return {
                    "message": "官方插件数量已刷新",
                    "official_total": official_total,
                    "timestamp": datetime.now().isoformat()
                }
            except Exception as e:
                logger.error(f"刷新官方插件数量失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/download-plugins")
        async def download_plugins_package(plugin_ids: List[str] = None):
            """打包下载插件"""
            try:
                import zipfile
                import tempfile
                from pathlib import Path

                # 获取要打包的插件
                if plugin_ids:
                    plugins = [self.db.get_plugin(pid) for pid in plugin_ids if self.db.get_plugin(pid)]
                    plugins = [p for p in plugins if p and p.local_path]
                else:
                    # 下载所有已下载的插件
                    plugins = self.db.get_all_plugins(PluginStatus.DOWNLOADED)
                    plugins.extend(self.db.get_all_plugins(PluginStatus.UPDATED))

                if not plugins:
                    raise HTTPException(status_code=404, detail="没有找到可下载的插件")

                # 创建临时zip文件
                temp_dir = Path(tempfile.gettempdir())
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                zip_filename = f"obsidian_plugins_{timestamp}.zip"
                zip_path = temp_dir / zip_filename

                with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for plugin in plugins:
                        if plugin.local_path and Path(plugin.local_path).exists():
                            plugin_dir = Path(plugin.local_path)

                            # 添加插件文件到zip
                            for file_path in plugin_dir.rglob("*"):
                                if file_path.is_file():
                                    # 在zip中的相对路径
                                    arcname = f"{plugin.id}/{file_path.name}"
                                    zipf.write(file_path, arcname)

                logger.info(f"创建插件包: {zip_path}, 包含 {len(plugins)} 个插件")

                return FileResponse(
                    path=zip_path,
                    filename=zip_filename,
                    media_type='application/zip'
                )

            except Exception as e:
                logger.error(f"创建插件包失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/config")
        async def get_config():
            """获取配置"""
            return config.model_dump()

        @self.app.post("/api/config")
        async def update_config(config_data: Dict[str, Any]):
            """更新配置"""
            try:
                # 更新配置
                for key, value in config_data.items():
                    if hasattr(config, key):
                        setattr(config, key, value)

                # 保存配置
                from .config import save_config
                save_config(config)

                return {"message": "配置已更新"}

            except Exception as e:
                logger.error(f"更新配置失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/analytics/rankings")
        async def get_rankings(limit: int = 50):
            """获取插件排行榜"""
            try:
                return analytics.get_plugin_rankings(limit)
            except Exception as e:
                logger.error(f"获取排行榜失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/analytics/timeline")
        async def get_timeline(days: int = 30):
            """获取插件时间线"""
            try:
                return analytics.get_plugin_timeline(days)
            except Exception as e:
                logger.error(f"获取时间线失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/analytics/authors")
        async def get_author_analysis():
            """获取作者分析"""
            try:
                return analytics.get_author_analysis()
            except Exception as e:
                logger.error(f"获取作者分析失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/analytics/categories")
        async def get_categories():
            """获取插件类别分析"""
            try:
                return analytics.get_plugin_categories()
            except Exception as e:
                logger.error(f"获取类别分析失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/analytics/export")
        async def export_data(format: str = "json"):
            """导出插件数据"""
            try:
                if format not in ["json", "csv", "excel"]:
                    raise HTTPException(status_code=400, detail="不支持的格式")

                file_path = analytics.export_plugin_data(format)
                return {"message": "数据导出成功", "file_path": file_path}
            except Exception as e:
                logger.error(f"导出数据失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/analytics/report")
        async def generate_report():
            """生成分析报告"""
            try:
                report_path = analytics.generate_report()
                return {"message": "报告生成成功", "report_path": report_path}
            except Exception as e:
                logger.error(f"生成报告失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    async def _run_sync(self):
        """运行同步任务"""
        try:
            await self.plugin_manager.sync_all_plugins()
        except Exception as e:
            logger.error(f"同步任务失败: {e}")
    
    async def _run_update(self):
        """运行更新任务"""
        try:
            await self.plugin_manager.update_outdated_plugins()
        except Exception as e:
            logger.error(f"更新任务失败: {e}")
    
    def _get_status_display(self, status):
        """获取状态显示文本"""
        status_map = {
            PluginStatus.DOWNLOADED: "已下载",
            PluginStatus.UPDATED: "已更新",
            PluginStatus.ERROR: "错误",
            PluginStatus.UNKNOWN: "未知",
            PluginStatus.AVAILABLE: "可用"
        }
        return status_map.get(status, str(status))

    def _count_plugin_files(self, local_path):
        """统计插件文件数量"""
        if not local_path:
            return 0
        try:
            from pathlib import Path
            plugin_dir = Path(local_path)
            if plugin_dir.exists():
                return len(list(plugin_dir.glob("*.js"))) + len(list(plugin_dir.glob("*.json"))) + len(list(plugin_dir.glob("*.css")))
            return 0
        except:
            return 0

    def run(self):
        """运行服务器"""
        import uvicorn
        logger.info(f"启动Web服务器: http://{self.host}:{self.port}")
        uvicorn.run(self.app, host=self.host, port=self.port, log_level="info")


# 全局Web服务器实例
web_server = WebServer()
