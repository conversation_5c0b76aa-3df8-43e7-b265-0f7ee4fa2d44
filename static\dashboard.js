// 全局变量
let currentPage = 1;
let currentStatus = '';
let currentSearch = '';
let schedulerRunning = false;
let autoRefreshInterval = null;
let currentViewMode = 'list'; // 'list' or 'card'

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    refreshData();
    loadPlugins();

    // 启动自动刷新（每30秒）
    startAutoRefresh();

    // 设置搜索框事件
    document.getElementById('search-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loadPlugins();
        }
    });

    // 设置状态过滤器事件
    document.getElementById('status-filter').addEventListener('change', function() {
        loadPlugins();
    });

    // 设置视图切换事件
    document.getElementById('list-view').addEventListener('change', function() {
        if (this.checked) {
            currentViewMode = 'list';
            loadPlugins();
        }
    });

    document.getElementById('card-view').addEventListener('change', function() {
        if (this.checked) {
            currentViewMode = 'card';
            loadPlugins();
        }
    });
});

// 启动自动刷新
function startAutoRefresh() {
    // 每30秒自动刷新状态
    autoRefreshInterval = setInterval(() => {
        refreshData();
    }, 30000);
}

// 停止自动刷新
function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

// 刷新数据
async function refreshData() {
    try {
        const response = await fetch('/api/status');
        const data = await response.json();
        
        updateStatusCards(data);
    } catch (error) {
        console.error('刷新数据失败:', error);
        showAlert('刷新数据失败', 'danger');
    }
}

// 更新状态卡片
function updateStatusCards(data) {
    const plugins = data.plugins;
    const scheduler = data.scheduler;

    console.log('更新状态数据:', data); // 调试日志

    // 更新各种统计数据
    const officialTotal = data.official_total || 0;
    const localTotal = data.local_total || 0;
    const downloadedCount = data.downloaded_count || 0;
    const failedCount = data.failed_count || 0;
    const syncProgress = data.sync_progress || 0;

    document.getElementById('official-total').textContent = officialTotal;
    document.getElementById('local-total').textContent = localTotal;
    document.getElementById('downloaded-plugins').textContent = downloadedCount;
    document.getElementById('failed-plugins').textContent = failedCount;
    document.getElementById('sync-progress').textContent = `${syncProgress.toFixed(2)}%`;

    // 更新进度条
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const progressDetail = document.getElementById('progress-detail');

    const progress = data.sync_progress || 0;
    progressBar.style.width = `${progress}%`;
    progressText.textContent = `${progress}%`;

    // 设置进度条颜色
    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
    if (progress >= 90) {
        progressBar.classList.add('bg-success');
    } else if (progress >= 50) {
        progressBar.classList.add('bg-info');
    } else if (progress >= 20) {
        progressBar.classList.add('bg-warning');
    } else {
        progressBar.classList.add('bg-danger');
    }

    progressDetail.textContent = `已下载 ${data.downloaded_count}/${data.official_total} 个插件，失败 ${data.failed_count} 个`;

    // 更新调度器状态
    schedulerRunning = scheduler.running;
    document.getElementById('scheduler-status').textContent = scheduler.running ? '运行中' : '已停止';

    // 更新最后更新时间
    const updateTime = new Date(data.timestamp);
    document.getElementById('last-update-time').textContent = updateTime.toLocaleTimeString('zh-CN');

    // 显示失败插件提醒
    const failedAlert = document.getElementById('failed-plugins-alert');
    const failedList = document.getElementById('failed-plugins-list');

    if (data.failed_count > 0 && data.failed_plugins && data.failed_plugins.length > 0) {
        let failedHtml = '<p>以下插件同步失败：</p><ul>';
        data.failed_plugins.forEach(plugin => {
            failedHtml += `<li><strong>${plugin.name}</strong> (${plugin.id}): ${plugin.error || '未知错误'}</li>`;
        });
        failedHtml += '</ul>';

        if (data.failed_count > data.failed_plugins.length) {
            failedHtml += `<p><small>还有 ${data.failed_count - data.failed_plugins.length} 个失败插件未显示...</small></p>`;
        }

        failedList.innerHTML = failedHtml;
        failedAlert.style.display = 'block';
    } else {
        failedAlert.style.display = 'none';
    }
}

// 加载插件列表
async function loadPlugins(page = 1) {
    currentPage = page;
    currentStatus = document.getElementById('status-filter').value;
    currentSearch = document.getElementById('search-input').value;
    
    document.getElementById('plugins-loading').style.display = 'block';
    document.getElementById('plugins-container').style.display = 'none';
    
    try {
        const params = new URLSearchParams({
            page: page,
            limit: 20
        });
        
        if (currentStatus) params.append('status', currentStatus);
        if (currentSearch) params.append('search', currentSearch);
        
        const response = await fetch(`/api/plugins?${params}`);
        const data = await response.json();
        
        displayPlugins(data.plugins);
        displayPagination(data);
        
        document.getElementById('plugins-loading').style.display = 'none';
        document.getElementById('plugins-container').style.display = 'block';
        
    } catch (error) {
        console.error('加载插件列表失败:', error);
        showAlert('加载插件列表失败', 'danger');
        document.getElementById('plugins-loading').style.display = 'none';
    }
}

// 显示插件列表
function displayPlugins(plugins) {
    const container = document.getElementById('plugins-list');

    if (plugins.length === 0) {
        container.innerHTML = '<div class="text-center text-muted">没有找到插件</div>';
        return;
    }

    if (currentViewMode === 'card') {
        displayPluginsCard(plugins, container);
    } else {
        displayPluginsList(plugins, container);
    }
}

// 列表视图
function displayPluginsList(plugins, container) {
    const html = plugins.map(plugin => {
        const statusClass = getStatusClass(plugin.status);
        const statusIcon = getStatusIcon(plugin.status);
        const hasUpdate = plugin.has_update;
        const updateBadge = hasUpdate ? '<span class="badge bg-warning text-dark ms-1">有更新</span>' : '';
        const localPathLink = plugin.local_path ?
            `<a href="#" onclick="openLocalPath('${plugin.local_path}')" class="text-decoration-none">
                <i class="bi bi-folder"></i> ${plugin.local_path}
            </a>` : '无';

        return `
            <div class="card plugin-card ${statusClass} mb-3">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="card-title">
                                ${statusIcon} ${plugin.name}
                                <small class="text-muted">(${plugin.id})</small>
                                ${updateBadge}
                            </h6>
                            <p class="card-text text-muted small">${plugin.description || '暂无描述'}</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="bi bi-person"></i> 作者: ${plugin.author}<br>
                                        <i class="bi bi-github"></i> 仓库: <a href="${plugin.github_url}" target="_blank">${plugin.repo}</a><br>
                                        <i class="bi bi-folder"></i> 本地: ${localPathLink}
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="bi bi-download"></i> 下载量: ${plugin.download_count || 0}<br>
                                        <i class="bi bi-files"></i> 文件数: ${plugin.file_count || 0}<br>
                                        <i class="bi bi-clock"></i> 更新: ${plugin.last_updated_display}
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="mb-2">
                                <span class="badge ${getStatusBadgeClass(plugin.status)}">${plugin.status_display}</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">
                                    <strong>当前版本:</strong> ${plugin.current_version || '未下载'}<br>
                                    <strong>最新版本:</strong> ${plugin.latest_version || '未知'}<br>
                                    <strong>最后检查:</strong> ${plugin.last_checked_display}
                                </small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" onclick="showPluginDetail('${plugin.id}')">
                                <i class="bi bi-info-circle"></i> 详情
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = html;
}

// 卡片视图
function displayPluginsCard(plugins, container) {
    const html = `
        <div class="row">
            ${plugins.map(plugin => {
                const statusIcon = getStatusIcon(plugin.status);
                const hasUpdate = plugin.has_update;
                const updateBadge = hasUpdate ? '<span class="badge bg-warning text-dark ms-1">有更新</span>' : '';
                const localPathLink = plugin.local_path ?
                    `<a href="#" onclick="openLocalPath('${plugin.local_path}')" class="text-decoration-none">
                        <i class="bi bi-folder"></i>
                    </a>` : '';

                return `
                    <div class="col-md-4 col-lg-3 mb-3">
                        <div class="card h-100 plugin-card ${getStatusClass(plugin.status)}">
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title">
                                    ${statusIcon} ${plugin.name}
                                    ${updateBadge}
                                </h6>
                                <p class="card-text text-muted small flex-grow-1">${(plugin.description || '暂无描述').substring(0, 80)}${plugin.description && plugin.description.length > 80 ? '...' : ''}</p>
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="badge ${getStatusBadgeClass(plugin.status)}">${plugin.status_display}</span>
                                        <small class="text-muted">${localPathLink}</small>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-person"></i> ${plugin.author}<br>
                                            <i class="bi bi-download"></i> ${plugin.download_count || 0}
                                        </small>
                                        <button class="btn btn-sm btn-outline-primary" onclick="showPluginDetail('${plugin.id}')">
                                            详情
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('')}
        </div>
    `;

    container.innerHTML = html;
}

// 显示分页
function displayPagination(data) {
    const container = document.getElementById('pagination');
    const { page, pages, total } = data;
    
    if (pages <= 1) {
        container.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // 上一页
    if (page > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadPlugins(${page - 1})">上一页</a></li>`;
    }
    
    // 页码
    const start = Math.max(1, page - 2);
    const end = Math.min(pages, page + 2);
    
    for (let i = start; i <= end; i++) {
        const active = i === page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadPlugins(${i})">${i}</a></li>`;
    }
    
    // 下一页
    if (page < pages) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadPlugins(${page + 1})">下一页</a></li>`;
    }
    
    container.innerHTML = html;
}

// 获取状态样式类
function getStatusClass(status) {
    switch (status) {
        case 'downloaded': return 'downloaded';
        case 'updated': return 'updated';
        case 'error': return 'error';
        default: return '';
    }
}

// 获取状态图标
function getStatusIcon(status) {
    switch (status) {
        case 'downloaded': return '<i class="bi bi-check-circle text-success"></i>';
        case 'updated': return '<i class="bi bi-arrow-up-circle text-info"></i>';
        case 'error': return '<i class="bi bi-exclamation-circle text-danger"></i>';
        case 'available': return '<i class="bi bi-cloud text-primary"></i>';
        case 'unknown': return '<i class="bi bi-question-circle text-muted"></i>';
        default: return '<i class="bi bi-question-circle text-muted"></i>';
    }
}

// 获取状态徽章颜色
function getStatusBadgeClass(status) {
    switch (status) {
        case 'downloaded': return 'bg-success';
        case 'updated': return 'bg-info';
        case 'error': return 'bg-danger';
        case 'available': return 'bg-primary';
        case 'unknown': return 'bg-secondary';
        default: return 'bg-secondary';
    }
}

// 显示插件详情
async function showPluginDetail(pluginId) {
    try {
        const response = await fetch(`/api/plugins/${pluginId}`);
        const plugin = await response.json();
        
        document.getElementById('pluginModalTitle').textContent = plugin.name;
        
        const html = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>ID:</td><td>${plugin.id}</td></tr>
                        <tr><td>名称:</td><td>${plugin.name}</td></tr>
                        <tr><td>作者:</td><td>${plugin.author}</td></tr>
                        <tr><td>状态:</td><td><span class="badge bg-secondary">${plugin.status}</span></td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>版本信息</h6>
                    <table class="table table-sm">
                        <tr><td>当前版本:</td><td>${plugin.current_version || '未下载'}</td></tr>
                        <tr><td>最新版本:</td><td>${plugin.latest_version || '未知'}</td></tr>
                        <tr><td>最后更新:</td><td>${plugin.last_updated ? new Date(plugin.last_updated).toLocaleString('zh-CN') : '从未'}</td></tr>
                        <tr><td>最后检查:</td><td>${plugin.last_checked ? new Date(plugin.last_checked).toLocaleString('zh-CN') : '从未'}</td></tr>
                    </table>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <h6>描述</h6>
                    <p>${plugin.description}</p>
                    <h6>仓库</h6>
                    <a href="https://github.com/${plugin.repo}" target="_blank">${plugin.repo}</a>
                    ${plugin.local_path ? `<h6 class="mt-3">本地路径</h6><code>${plugin.local_path}</code>` : ''}
                    ${plugin.error_message ? `<h6 class="mt-3">错误信息</h6><div class="alert alert-danger">${plugin.error_message}</div>` : ''}
                </div>
            </div>
        `;
        
        document.getElementById('pluginModalBody').innerHTML = html;
        
        const modal = new bootstrap.Modal(document.getElementById('pluginModal'));
        modal.show();
        
    } catch (error) {
        console.error('获取插件详情失败:', error);
        showAlert('获取插件详情失败', 'danger');
    }
}

// 开始同步
async function startSync() {
    if (confirm('确定要开始同步所有插件吗？这可能需要一些时间。')) {
        try {
            const response = await fetch('/api/sync', { method: 'POST' });
            const data = await response.json();
            showAlert(data.message, 'success');
        } catch (error) {
            console.error('启动同步失败:', error);
            showAlert('启动同步失败', 'danger');
        }
    }
}

// 开始更新
async function startUpdate() {
    if (confirm('确定要更新过期插件吗？')) {
        try {
            const response = await fetch('/api/update', { method: 'POST' });
            const data = await response.json();
            showAlert(data.message, 'success');
        } catch (error) {
            console.error('启动更新失败:', error);
            showAlert('启动更新失败', 'danger');
        }
    }
}

// 切换调度器状态
async function toggleScheduler() {
    const action = schedulerRunning ? 'stop' : 'start';
    const actionText = schedulerRunning ? '停止' : '启动';
    
    if (confirm(`确定要${actionText}调度器吗？`)) {
        try {
            const response = await fetch(`/api/scheduler/${action}`, { method: 'POST' });
            const data = await response.json();
            showAlert(data.message, 'success');
            refreshData();
        } catch (error) {
            console.error(`${actionText}调度器失败:`, error);
            showAlert(`${actionText}调度器失败`, 'danger');
        }
    }
}

// 刷新官方插件数量
async function refreshOfficialCount() {
    try {
        showAlert('正在刷新官方插件数量...', 'info');
        const response = await fetch('/api/refresh-official', { method: 'POST' });
        const data = await response.json();
        showAlert(`官方插件数量已刷新: ${data.official_total}`, 'success');
        refreshData(); // 刷新状态
    } catch (error) {
        console.error('刷新官方数量失败:', error);
        showAlert('刷新官方数量失败', 'danger');
    }
}

// 打包下载所有插件
async function downloadAllPlugins() {
    if (confirm('确定要打包下载所有已下载的插件吗？')) {
        try {
            showAlert('正在创建插件包...', 'info');
            const response = await fetch('/api/download-plugins', { method: 'POST' });

            if (response.ok) {
                // 创建下载链接
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `obsidian_plugins_${new Date().toISOString().slice(0,10)}.zip`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                showAlert('插件包下载开始', 'success');
            } else {
                throw new Error('下载失败');
            }
        } catch (error) {
            console.error('打包下载失败:', error);
            showAlert('打包下载失败', 'danger');
        }
    }
}

// 打开本地路径
function openLocalPath(path) {
    // 在Web环境中，我们只能显示路径，无法直接打开文件夹
    showAlert(`本地路径: ${path}`, 'info');

    // 复制路径到剪贴板
    if (navigator.clipboard) {
        navigator.clipboard.writeText(path).then(() => {
            showAlert('路径已复制到剪贴板', 'success');
        }).catch(() => {
            showAlert('无法复制路径', 'warning');
        });
    }
}

// 显示提示信息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}
