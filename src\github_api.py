"""GitHub API集成模块"""
import asyncio
import aiohttp
import aiofiles
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from .models import PluginInfo, PluginManifest, GitHubRelease
from .config import config
from .logger import logger


class GitHubAPI:
    """GitHub API客户端"""
    
    def __init__(self):
        self.base_url = "https://api.github.com"
        self.session: Optional[aiohttp.ClientSession] = None
        self.semaphore = asyncio.Semaphore(config.sync.max_concurrent_downloads)
        
        self.headers = {
            "User-Agent": "Obsidian-Plugin-Sync/1.0.0",
            "Accept": "application/vnd.github.v3+json"
        }
        
        if config.github.token:
            self.headers["Authorization"] = f"token {config.github.token}"
    
    async def __aenter__(self):
        timeout = aiohttp.ClientTimeout(total=config.github.timeout)
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=timeout
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def download_plugin_files(self, plugin: PluginInfo, version: str, target_dir: Path) -> bool:
        """下载插件文件"""
        async with self.semaphore:
            try:
                logger.info(f"开始下载插件: {plugin.name} v{version}")
                
                # 创建插件目录
                plugin_dir = target_dir / plugin.id
                plugin_dir.mkdir(parents=True, exist_ok=True)
                
                # 需要下载的文件
                files_to_download = [
                    ("manifest.json", True),   # 必需文件
                    ("main.js", True),         # 必需文件
                    ("styles.css", False),     # 可选文件
                ]
                
                success_count = 0
                total_required = sum(1 for _, required in files_to_download if required)
                
                for filename, required in files_to_download:
                    success = await self._download_file(
                        plugin.repo, version, filename, plugin_dir / filename
                    )
                    
                    if success:
                        success_count += 1
                    elif required:
                        logger.error(f"下载必需文件失败: {plugin.id}/{filename}")
                        return False
                
                # 检查是否下载了所有必需文件
                if success_count >= total_required:
                    logger.info(f"插件 {plugin.name} 下载完成")
                    return True
                else:
                    logger.error(f"插件 {plugin.name} 下载失败，缺少必需文件")
                    return False
                    
            except Exception as e:
                logger.error(f"下载插件 {plugin.name} 时发生错误: {e}")
                return False
    
    async def _download_file(self, repo: str, version: str, filename: str, target_path: Path) -> bool:
        """下载单个文件"""
        url = f"https://github.com/{repo}/releases/download/{version}/{filename}"
        
        try:
            await asyncio.sleep(config.github.request_interval)
            
            async with self.session.get(url) as response:
                if response.status == 404:
                    logger.debug(f"文件不存在: {filename} (这可能是正常的，如styles.css)")
                    return False
                
                response.raise_for_status()
                
                # 写入文件
                async with aiofiles.open(target_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
                
                logger.debug(f"文件下载成功: {filename}")
                return True
                
        except Exception as e:
            logger.error(f"下载文件 {filename} 失败: {e}")
            return False
    
    async def get_plugin_download_stats(self, repo: str) -> Dict[str, int]:
        """获取插件下载统计"""
        try:
            await asyncio.sleep(config.github.request_interval)
            
            # 获取所有发布版本
            releases_url = f"{self.base_url}/repos/{repo}/releases"
            async with self.session.get(releases_url) as response:
                if response.status == 404:
                    return {"total_downloads": 0}
                
                response.raise_for_status()
                releases = await response.json()
                
                total_downloads = 0
                for release in releases:
                    for asset in release.get("assets", []):
                        total_downloads += asset.get("download_count", 0)
                
                return {
                    "total_downloads": total_downloads,
                    "releases_count": len(releases)
                }
                
        except Exception as e:
            logger.error(f"获取下载统计失败 {repo}: {e}")
            return {"total_downloads": 0}
    
    async def check_plugin_updates(self, plugins: List[Tuple[PluginInfo, str]]) -> List[Tuple[PluginInfo, str, str]]:
        """批量检查插件更新"""
        logger.info(f"检查 {len(plugins)} 个插件的更新...")
        
        updates = []
        for plugin, current_version in plugins:
            try:
                # 获取最新版本
                manifest_url = f"https://raw.githubusercontent.com/{plugin.repo}/master/manifest.json"
                
                await asyncio.sleep(config.github.request_interval)
                async with self.session.get(manifest_url) as response:
                    if response.status == 200:
                        manifest_data = await response.json()
                        latest_version = manifest_data.get("version", "")
                        
                        if latest_version and latest_version != current_version:
                            updates.append((plugin, current_version, latest_version))
                            logger.info(f"发现更新: {plugin.name} {current_version} -> {latest_version}")
                    
            except Exception as e:
                logger.error(f"检查插件 {plugin.name} 更新失败: {e}")
        
        logger.info(f"发现 {len(updates)} 个插件有更新")
        return updates
    
    async def validate_plugin_files(self, plugin_dir: Path) -> bool:
        """验证插件文件完整性"""
        required_files = ["manifest.json", "main.js"]
        
        for filename in required_files:
            file_path = plugin_dir / filename
            if not file_path.exists() or file_path.stat().st_size == 0:
                logger.error(f"插件文件缺失或为空: {file_path}")
                return False
        
        # 验证manifest.json格式
        try:
            import json
            manifest_path = plugin_dir / "manifest.json"
            async with aiofiles.open(manifest_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                manifest_data = json.loads(content)
                
                required_fields = ["id", "name", "version", "minAppVersion"]
                for field in required_fields:
                    if field not in manifest_data:
                        logger.error(f"manifest.json缺少必需字段: {field}")
                        return False
                
        except Exception as e:
            logger.error(f"验证manifest.json失败: {e}")
            return False
        
        return True
